// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "wbCamer",
    platforms: [
        .iOS(.v16)
    ],
    products: [
        .library(
            name: "wbCamer",
            targets: ["wbCamer"]
        )
    ],
    dependencies: [
        // WebRTC iOS SDK
        .package(url: "https://github.com/stasel/WebRTC.git", from: "118.0.0"),
        // WebSocket library
        .package(url: "https://github.com/daltoniam/Starscream.git", from: "4.0.0"),
        // Network reachability
        .package(url: "https://github.com/ashleymills/Reachability.swift.git", from: "5.0.0")
    ],
    targets: [
        .target(
            name: "wbCamer",
            dependencies: [
                .product(name: "WebRTC", package: "WebRTC"),
                .product(name: "Starscream", package: "Starscream"),
                .product(name: "Reachability", package: "Reachability.swift")
            ]
        ),
        .testTarget(
            name: "wbCamerTests",
            dependencies: ["wbCamer"]
        )
    ]
)