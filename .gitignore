.DS_Store
.vscode
.env
.env.local
.env.development
.env.production
.cursor
Doc
xcuserdata/
*.xcscmblueprint
*.xccheckout
build/
DerivedData/
*.moved-aside
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
*.hmap
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
*.xcodeproj/xcshareddat/xcuserdata/*
!*.xcworkspace/contents.xcworkspacedata
*.xcworkspace/xcuserdata/*
/*.gcno
*.ipa
*.dSYM.zip
*.dSYM
Pods/
Podfile.lock
*.xcuserstate
Carthage/Build/
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# Swift Package Manager
.build/
.swiftpm/
Package.resolved

# SwiftLint
.swiftlint-cache/

# Xcode
*.xcworkspace/xcshareddata/
*.xcworkspace/xcuserdata/

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk
