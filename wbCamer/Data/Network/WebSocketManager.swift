//
//  WebSocketManager.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Combine
import Network
import UIKit
import os.log

// MARK: - WebSocket Connection State

enum WebSocketState: Equatable {
    case disconnected
    case connecting
    case connected
    case reconnecting
    case failed(Error)

    static func == (lhs: WebSocketState, rhs: WebSocketState) -> Bool {
        switch (lhs, rhs) {
        case (.disconnected, .disconnected),
             (.connecting, .connecting),
             (.connected, .connected),
             (.reconnecting, .reconnecting):
            return true
        case (.failed, .failed):
            return true
        default:
            return false
        }
    }
}

// MARK: - WebSocket Error Types

enum WebSocketError: Error, LocalizedError {
    case connectionFailed
    case invalidURL
    case tlsHandshakeFailed
    case messageSerializationFailed
    case heartbeatTimeout
    case maxReconnectAttemptsReached
    
    var errorDescription: String? {
        switch self {
        case .connectionFailed:
            return "WebSocket connection failed"
        case .invalidURL:
            return "Invalid WebSocket URL"
        case .tlsHandshakeFailed:
            return "TLS handshake failed"
        case .messageSerializationFailed:
            return "Failed to serialize message"
        case .heartbeatTimeout:
            return "Heartbeat timeout"
        case .maxReconnectAttemptsReached:
            return "Maximum reconnection attempts reached"
        }
    }
}

// MARK: - WebSocket Manager

class WebSocketManager: NSObject, ObservableObject {
    static let shared = WebSocketManager()
    
    // MARK: - Published Properties
    @Published var connectionState: WebSocketState = .disconnected
    @Published var isConnected: Bool = false
    
    // MARK: - Private Properties
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private let messageQueue = DispatchQueue(label: "websocket.message.queue", qos: .userInitiated)
    private let heartbeatQueue = DispatchQueue(label: "websocket.heartbeat.queue")
    
    // Configuration
    private var baseURL: String
    private let heartbeatInterval: TimeInterval = 11.0 // 11 seconds as specified
    private let maxReconnectAttempts = 5
    private let reconnectDelay: TimeInterval = 2.0
    
    // State management
    private var reconnectAttempts = 0
    private var heartbeatTimeoutTimer: Timer?  // 改为超时检测定时器
    private var lastHeartbeatReceived: Date?   // 最后收到心跳的时间
    private var messageBuffer: [WebSocketMessage] = []
    private var isReconnecting = false
    
    // Combine
    private var cancellables = Set<AnyCancellable>()
    private let messageSubject = PassthroughSubject<WebSocketMessage, Never>()
    private let errorSubject = PassthroughSubject<WebSocketError, Never>()
    
    // Logger
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "WebSocket")

    // 日志控制开关 - 设为false来屏蔽详细WebSocket日志
    private let enableVerboseLogging = false
    
    // MARK: - Publishers
    var messagePublisher: AnyPublisher<WebSocketMessage, Never> {
        messageSubject.eraseToAnyPublisher()
    }
    
    var errorPublisher: AnyPublisher<WebSocketError, Never> {
        errorSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    
    init(baseURL: String = "ws://192.168.1.100:8080") {
        self.baseURL = baseURL
        super.init()
        setupURLSession()
    }
    
    private func setupURLSession() {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30.0
        configuration.timeoutIntervalForResource = 60.0
        
        urlSession = URLSession(configuration: configuration, delegate: self, delegateQueue: nil)
    }
    
    // MARK: - Configuration Management

    func updateBaseURL(to urlString: String) {
        // Disconnect if currently connected
        if connectionState == .connected {
            disconnect()
        }

        self.baseURL = urlString
        if enableVerboseLogging {
            print("WebSocketManager base URL updated to: \(urlString)")
        }
    }

    // MARK: - Helper Methods

    private func generateWebSocketKey() -> String {
        let keyData = Data((0..<16).map { _ in UInt8.random(in: 0...255) })
        return keyData.base64EncodedString()
    }

    // MARK: - Connection Management

    func connect() {
        guard connectionState != .connected && connectionState != .connecting else {
            logger.info("WebSocket already connected or connecting")
            return
        }

        // 使用传入的baseURL，不强制修改端口
        let wsURL = baseURL

        guard let url = URL(string: wsURL) else {
            logger.error("Invalid WebSocket URL: \(wsURL)")
            updateConnectionState(.failed(WebSocketError.invalidURL))
            return
        }
        
        logger.info("Connecting to WebSocket: \(url.absoluteString)")
        updateConnectionState(.connecting)
        
        var request = URLRequest(url: url)

        // 基于浏览器成功连接P2-R1的事实，完全模拟浏览器的WebSocket请求
        // 设置标准WebSocket升级头部
        request.setValue("websocket", forHTTPHeaderField: "Upgrade")
        request.setValue("Upgrade", forHTTPHeaderField: "Connection")
        request.setValue("13", forHTTPHeaderField: "Sec-WebSocket-Version")
        request.setValue(generateWebSocketKey(), forHTTPHeaderField: "Sec-WebSocket-Key")

        // 关键：设置与浏览器完全相同的头部
        request.setValue("http://\(url.host ?? "")", forHTTPHeaderField: "Origin")
        request.setValue("Mozilla/5.0 (iPhone; CPU iPhone OS 18_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1", forHTTPHeaderField: "User-Agent")

        // 浏览器标准头部
        request.setValue("gzip, deflate", forHTTPHeaderField: "Accept-Encoding")
        request.setValue("en-US,en;q=0.9", forHTTPHeaderField: "Accept-Language")
        request.setValue("same-origin", forHTTPHeaderField: "Sec-Fetch-Site")
        request.setValue("websocket", forHTTPHeaderField: "Sec-Fetch-Mode")
        request.setValue("empty", forHTTPHeaderField: "Sec-Fetch-Dest")

        // 设置合理的超时
        request.timeoutInterval = 30.0

        // Add authentication if available (但P2-R1可能不需要)
        if let apiKey = UserDefaults.standard.string(forKey: "api_key") {
            request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        }

        // 打印详细的请求信息用于调试
        if enableVerboseLogging {
            print("🔍 WebSocket Request Details (模拟浏览器):")
            print("   URL: \(url.absoluteString)")
            print("   Headers:")
            for (key, value) in request.allHTTPHeaderFields ?? [:] {
                print("     \(key): \(value)")
            }
        }
        
        webSocketTask = urlSession?.webSocketTask(with: request)
        webSocketTask?.resume()

        startReceiving()
        // P2-R1 uses passive heartbeat - we only monitor for server heartbeats
    }
    
    func disconnect() {
        logger.info("Disconnecting WebSocket")

        stopHeartbeatTimeoutMonitoring()
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil

        updateConnectionState(.disconnected)
        reconnectAttempts = 0
        isReconnecting = false
    }
    
    private func reconnect() {
        guard !isReconnecting && reconnectAttempts < maxReconnectAttempts else {
            if reconnectAttempts >= maxReconnectAttempts {
                logger.error("Max reconnection attempts reached")
                errorSubject.send(.maxReconnectAttemptsReached)
                updateConnectionState(.failed(WebSocketError.maxReconnectAttemptsReached))
            }
            return
        }
        
        isReconnecting = true
        reconnectAttempts += 1
        
        logger.info("Attempting to reconnect (\(self.reconnectAttempts)/\(self.maxReconnectAttempts))")
        updateConnectionState(.reconnecting)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + reconnectDelay) { [weak self] in
            self?.isReconnecting = false
            self?.connect()
        }
    }
    
    // MARK: - Message Handling
    
    private func startReceiving() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                self?.handleReceivedMessage(message)
                self?.startReceiving() // Continue receiving
                
            case .failure(let error):
                self?.logger.error("WebSocket receive error: \(error.localizedDescription)")
                self?.handleConnectionError(error)
            }
        }
    }
    
    private func handleReceivedMessage(_ message: URLSessionWebSocketTask.Message) {
        messageQueue.async { [weak self] in
            guard let self = self else { return }
            
            switch message {
            case .string(let text):
                // 只记录非心跳消息
                if !text.contains("\"what\":\"hb\"") && text.trimmingCharacters(in: .whitespacesAndNewlines) != "hb" {
                    //self.logger.debug("Received text message: \(text)")
                }
                self.processTextMessage(text)
                
            case .data(let data):
                self.logger.debug("Received binary message: \(data.count) bytes")
                self.processBinaryMessage(data)
                
            @unknown default:
                self.logger.warning("Received unknown message type")
            }
        }
    }
    
    private func processTextMessage(_ text: String) {
        guard let data = text.data(using: .utf8) else {
            logger.error("Failed to convert text message to data")
            return
        }

        // First try to parse as P2-R1 message format
        if let p2r1Message = parseP2R1Message(text) {
            handleP2R1Message(p2r1Message)
            return
        }

        // Fallback to standard WebSocket message format
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            let message = try decoder.decode(WebSocketMessage.self, from: data)

            DispatchQueue.main.async {
                self.messageSubject.send(message)
            }

        } catch {
            logger.error("Failed to decode WebSocket message: \(error.localizedDescription)")
            logger.debug("Raw message: \(text)")
        }
    }
    
    private func processBinaryMessage(_ data: Data) {
        // Handle binary messages if needed
        logger.debug("Processing binary message of \(data.count) bytes")
    }
    
    // MARK: - Message Sending
    
    func send<T: Codable>(_ message: T) {
        guard connectionState == .connected else {
            logger.warning("Cannot send message: WebSocket not connected")
            // Buffer the message for later sending
            if let wsMessage = message as? WebSocketMessage {
                messageBuffer.append(wsMessage)
            }
            return
        }
        
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(message)
            let text = String(data: data, encoding: .utf8) ?? ""
            
            let wsMessage = URLSessionWebSocketTask.Message.string(text)
            
            webSocketTask?.send(wsMessage) { [weak self] error in
                if let error = error {
                    self?.logger.error("Failed to send message: \(error.localizedDescription)")
                    self?.errorSubject.send(.messageSerializationFailed)
                } else {
                    self?.logger.debug("Message sent successfully")
                }
            }
            
        } catch {
            logger.error("Failed to encode message: \(error.localizedDescription)")
            errorSubject.send(.messageSerializationFailed)
        }
    }
    
    private func sendBufferedMessages() {
        guard !self.messageBuffer.isEmpty else { return }

        logger.info("Sending \(self.messageBuffer.count) buffered messages")

        for message in self.messageBuffer {
            send(message)
        }

        self.messageBuffer.removeAll()
    }
    
    // MARK: - P2-R1 Heartbeat Management (Passive)

    private func startHeartbeatTimeoutMonitoring() {
        stopHeartbeatTimeoutMonitoring()
        resetHeartbeatTimeout()
        logger.debug("Started P2-R1 heartbeat timeout monitoring")
    }

    private func stopHeartbeatTimeoutMonitoring() {
        heartbeatTimeoutTimer?.invalidate()
        heartbeatTimeoutTimer = nil
        logger.debug("Stopped heartbeat timeout monitoring")
    }

    private func resetHeartbeatTimeout() {
        heartbeatTimeoutTimer?.invalidate()
        heartbeatTimeoutTimer = Timer.scheduledTimer(withTimeInterval: heartbeatInterval, repeats: false) { [weak self] _ in
            self?.logger.warning("P2-R1 heartbeat timeout - closing connection")
            self?.handleConnectionError(WebSocketError.heartbeatTimeout)
        }
        lastHeartbeatReceived = Date()
    }
    
    // MARK: - State Management
    
    private func updateConnectionState(_ newState: WebSocketState) {
        DispatchQueue.main.async {
            self.connectionState = newState
            self.isConnected = (newState == .connected)
            
            if newState == .connected {
                self.reconnectAttempts = 0
                self.lastHeartbeatReceived = Date()
                self.startHeartbeatTimeoutMonitoring()
                self.sendBufferedMessages()
            }
        }
    }
    
    private func handleConnectionError(_ error: Error) {
        logger.error("WebSocket connection error: \(error.localizedDescription)")
        
        updateConnectionState(.failed(error))
        
        // Attempt to reconnect
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.reconnect()
        }
    }
}

// MARK: - URLSessionWebSocketDelegate

extension WebSocketManager: URLSessionWebSocketDelegate {
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didOpenWithProtocol protocol: String?) {
        let protocolName = `protocol` ?? "none"
        logger.info("WebSocket connection opened with protocol: \(protocolName)")
        updateConnectionState(.connected)
    }
    
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didCloseWith closeCode: URLSessionWebSocketTask.CloseCode, reason: Data?) {
        let reasonString = reason.flatMap { String(data: $0, encoding: .utf8) } ?? "No reason"
        logger.info("WebSocket connection closed with code: \(closeCode.rawValue), reason: \(reasonString)")
        
        updateConnectionState(.disconnected)
        
        // Attempt to reconnect unless it was a normal closure
        if closeCode != .normalClosure {
            reconnect()
        }
    }
}

// MARK: - URLSessionDelegate

extension WebSocketManager: URLSessionDelegate {
    func urlSession(_ session: URLSession, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
        
        // Handle SSL certificate validation
        guard challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust else {
            completionHandler(.performDefaultHandling, nil)
            return
        }
        
        // In production, implement proper certificate validation
        // For development, you might want to allow self-signed certificates
        
        #if DEBUG
        // Allow self-signed certificates in debug mode
        if let serverTrust = challenge.protectionSpace.serverTrust {
            let credential = URLCredential(trust: serverTrust)
            completionHandler(.useCredential, credential)
        } else {
            completionHandler(.performDefaultHandling, nil)
        }
        #else
        // In production, use proper certificate validation
        completionHandler(.performDefaultHandling, nil)
        #endif
    }
    
    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            let nsError = error as NSError
            logger.error("URLSession task completed with error: \(error.localizedDescription)")

            // 详细的错误分析
            if enableVerboseLogging {
                print("🔍 WebSocket Error Analysis:")
                print("   Domain: \(nsError.domain)")
                print("   Code: \(nsError.code)")
                print("   Description: \(nsError.localizedDescription)")
                print("   UserInfo: \(nsError.userInfo)")

                // 检查具体的错误类型
                if nsError.domain == NSURLErrorDomain {
                    switch nsError.code {
                    case NSURLErrorSecureConnectionFailed:
                        print("   ❌ TLS handshake failed")
                    case NSURLErrorNetworkConnectionLost:
                        print("   ❌ Network connection lost")
                    case NSURLErrorCannotConnectToHost:
                        print("   ❌ Cannot connect to host")
                    case NSURLErrorTimedOut:
                        print("   ❌ Connection timed out")
                    case NSURLErrorNotConnectedToInternet:
                        print("   ❌ Not connected to internet")
                    default:
                        print("   ❌ Other URL error: \(nsError.code)")
                    }
                }
            }

            // 错误处理逻辑（保留）
            if nsError.domain == NSURLErrorDomain {
                switch nsError.code {
                case NSURLErrorSecureConnectionFailed:
                    errorSubject.send(.tlsHandshakeFailed)
                default:
                    break
                }
            }

            handleConnectionError(error)
        }
    }
}

// MARK: - P2-R1 Message Handling

extension WebSocketManager {

    private func parseP2R1Message(_ text: String) -> [String: Any]? {
        guard let data = text.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              json["what"] != nil else {
            return nil
        }
        return json
    }

    private func handleP2R1Message(_ message: [String: Any]) {
        guard let what = message["what"] as? String else { return }

        //logger.debug("Received P2-R1 message: \(what)")

        switch what {
        case "hb":
            // P2-R1 heartbeat - reset timeout timer (静默处理)
            resetHeartbeatTimeout()

        case "ConfigChanged":
            handleConfigChanged(message)

        case "RecStart":
            handleRecordingStarted(message)

        case "RecStop":
            handleRecordingStopped(message)

        case "Shutdown":
            handleShutdown(message)

        case "SystemTimeChange":
            handleSystemTimeChange(message)

        case "UpdateStreamSetting":
            handleStreamSettingUpdate(message)

        case "ZoomUpdated":
            handleZoomUpdated(message)

        case "UpdateAfArea":
            handleAfAreaUpdate(message)

        case "PtzTraceSt":
            handlePtzTraceStatus(message)

        case "FramingStatus", "FramingStatusChanged", "FramingSettingChanged", "FramingInfoUpdate":
            handleFramingUpdate(message)

        case "AfDone":
            handleAfDone(message)

        case "GenlockSyncStatus":
            handleGenlockSyncStatus(message)

        case "PresetChange":
            handlePresetChange(message)

        case "RepairFileDone":
            handleRepairFileDone(message)

        case "StreamSettingsChanged":
            handleStreamSettingsChanged(message)

        case "PresetStatus":
            handlePresetStatus(message)

        case "NicknameChange":
            handleNicknameChange(message)

        case "PreRollStarted":
            handlePreRollStarted(message)

        case "ClearSetting":
            handleClearSetting(message)

        default:
            //logger.debug("Unknown P2-R1 message type: \(what)")
            // Forward unknown messages as generic WebSocket messages
            forwardAsGenericMessage(message)
        }
    }

    // MARK: - P2-R1 Message Handlers

    private func handleConfigChanged(_ message: [String: Any]) {
        guard let key = message["key"] as? String else { return }

        //logger.debug("Config changed: \(key)")

        // Create a status update message for the app
        let statusUpdate = StatusUpdateMessage(
            battery: key == "battery_voltage" ? Int(message["value"] as? String ?? "0") : nil,
            recording: key.contains("rec") ? (message["value"] as? String == "true") : nil,
            temperature: key == "temperature" ? Double(message["value"] as? String ?? "0") : nil,
            storage: nil
        )

        let wsMessage = WebSocketMessage(
            type: .statusUpdate,
            data: try? JSONEncoder().encode(statusUpdate),
            timestamp: Date()
        )

        DispatchQueue.main.async {
            self.messageSubject.send(wsMessage)
        }
    }

    private func handleRecordingStarted(_ message: [String: Any]) {
        let wsMessage = WebSocketMessage(
            type: .recordingStarted,
            data: nil,
            timestamp: Date()
        )

        DispatchQueue.main.async {
            self.messageSubject.send(wsMessage)
        }
    }

    private func handleRecordingStopped(_ message: [String: Any]) {
        let wsMessage = WebSocketMessage(
            type: .recordingStopped,
            data: nil,
            timestamp: Date()
        )

        DispatchQueue.main.async {
            self.messageSubject.send(wsMessage)
        }
    }

    private func handleShutdown(_ message: [String: Any]) {
        logger.warning("Camera is shutting down")
        disconnect()
    }

    private func handleSystemTimeChange(_ message: [String: Any]) {
        logger.debug("System time changed")
        // Could trigger time sync or UI updates
    }

    private func handleStreamSettingUpdate(_ message: [String: Any]) {
        logger.debug("Stream settings updated")
        // Could trigger stream configuration refresh
    }

    private func handleZoomUpdated(_ message: [String: Any]) {
        logger.debug("Zoom updated")
        // Could update zoom controls in UI
    }

    private func handleAfAreaUpdate(_ message: [String: Any]) {
        logger.debug("AF area updated")
        // Could update autofocus area display
    }

    private func handlePtzTraceStatus(_ message: [String: Any]) {
        logger.debug("PTZ trace status updated")
        // Could update PTZ controls
    }

    private func handleFramingUpdate(_ message: [String: Any]) {
        logger.debug("Framing status updated")
        // Could update framing controls
    }

    private func handleAfDone(_ message: [String: Any]) {
        logger.debug("Autofocus completed")
        // Could update focus status
    }

    private func handleGenlockSyncStatus(_ message: [String: Any]) {
        logger.debug("Genlock sync status updated")
        // Could update sync status display
    }

    private func handlePresetChange(_ message: [String: Any]) {
        logger.debug("Preset changed")
        // Could update preset controls
    }

    private func handleRepairFileDone(_ message: [String: Any]) {
        logger.debug("File repair completed")
        // Could refresh file list
    }

    private func handleStreamSettingsChanged(_ message: [String: Any]) {
        logger.debug("Stream settings changed")
        // Could update streaming controls
    }

    private func handlePresetStatus(_ message: [String: Any]) {
        logger.debug("Preset status updated")
        // Could update preset status display
    }

    private func handleNicknameChange(_ message: [String: Any]) {
        if let nickname = message["nickname"] as? String {
            logger.debug("Camera nickname changed to: \(nickname)")
            // Could update device name display
        }
    }

    private func handlePreRollStarted(_ message: [String: Any]) {
        logger.debug("Pre-roll started")
        // Could update recording status
    }

    private func handleClearSetting(_ message: [String: Any]) {
        logger.debug("Settings cleared")
        // Could trigger settings refresh
    }

    private func forwardAsGenericMessage(_ message: [String: Any]) {
        // Convert P2-R1 message to generic WebSocket message format
        if let data = try? JSONSerialization.data(withJSONObject: message) {
            let wsMessage = WebSocketMessage(
                type: .statusUpdate,
                data: data,
                timestamp: Date()
            )

            DispatchQueue.main.async {
                self.messageSubject.send(wsMessage)
            }
        }
    }
}
