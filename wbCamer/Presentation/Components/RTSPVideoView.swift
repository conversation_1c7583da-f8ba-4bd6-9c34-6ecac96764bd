//
//  RTSPVideoView.swift
//  wbCamer
//
//  Created by AI Assistant on 2025/7/1.
//

import SwiftUI
import AVFoundation
import UIKit

// MARK: - RTSP视频显示组件

struct RTSPVideoView: View {
    @ObservedObject var rtspPlayer: RTSPVideoPlayer
    let camera: CameraDevice
    
    var body: some View {
        Group {
            if rtspPlayer.isConnecting {
                // 连接中状态
                Rectangle()
                    .fill(Color.black)
                    .overlay(
                        VStack(spacing: 16) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.5)
                            Text("正在连接RTSP流...")
                                .font(.headline)
                                .foregroundColor(.white)
                            Text("尝试直连以降低延迟")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))
                        }
                    )
            } else if rtspPlayer.isConnected, let playerLayer = rtspPlayer.playerLayer {
                // 连接成功，显示视频
                RTSPPlayerLayerView(playerLayer: playerLayer)
                    .onAppear {
                        print("[RTSPVideoView] ✅ RTSP video view appeared")
                    }
            } else if let error = rtspPlayer.connectionError {
                // 连接失败
                Rectangle()
                    .fill(Color.black)
                    .overlay(
                        VStack(spacing: 16) {
                            Image(systemName: "exclamationmark.triangle")
                                .font(.system(size: 48))
                                .foregroundColor(.orange)
                            
                            Text("RTSP连接失败")
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            Text(error.localizedDescription)
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                            
                            Button("重试连接") {
                                Task {
                                    await rtspPlayer.connect(to: camera.ipAddress)
                                }
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 8)
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                        }
                    )
            } else {
                // 初始状态
                Rectangle()
                    .fill(Color.black)
                    .overlay(
                        VStack(spacing: 16) {
                            Image(systemName: "video")
                                .font(.system(size: 48))
                                .foregroundColor(.gray)
                            
                            Text("准备RTSP连接")
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            Text("直连RTSP流以获得最低延迟")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))
                        }
                    )
                    .onAppear {
                        // 自动开始连接
                        Task {
                            await rtspPlayer.connect(to: camera.ipAddress)
                        }
                    }
            }
        }
    }
}

// MARK: - RTSP播放器层视图

struct RTSPPlayerLayerView: UIViewRepresentable {
    let playerLayer: AVPlayerLayer
    
    func makeUIView(context: Context) -> RTSPPlayerContainerView {
        print("[RTSPPlayerLayerView] 📺 Creating RTSP player container view")
        let containerView = RTSPPlayerContainerView()
        containerView.setupPlayerLayer(playerLayer)
        return containerView
    }
    
    func updateUIView(_ uiView: RTSPPlayerContainerView, context: Context) {
        print("[RTSPPlayerLayerView] 📺 Updating RTSP player container view")
        uiView.updatePlayerLayer(playerLayer)
    }
}

// MARK: - RTSP播放器容器视图

class RTSPPlayerContainerView: UIView {
    private var currentPlayerLayer: AVPlayerLayer?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        backgroundColor = UIColor.black
        clipsToBounds = true
    }
    
    func setupPlayerLayer(_ playerLayer: AVPlayerLayer) {
        // 移除旧的播放器层
        currentPlayerLayer?.removeFromSuperlayer()
        
        // 添加新的播放器层
        playerLayer.frame = bounds
        playerLayer.videoGravity = .resizeAspect
        layer.addSublayer(playerLayer)
        currentPlayerLayer = playerLayer
        
        print("[RTSPPlayerContainerView] 📺 Player layer setup completed")
    }
    
    func updatePlayerLayer(_ playerLayer: AVPlayerLayer) {
        if currentPlayerLayer != playerLayer {
            setupPlayerLayer(playerLayer)
        } else {
            // 更新现有层的frame
            currentPlayerLayer?.frame = bounds
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        currentPlayerLayer?.frame = bounds
    }
}

// MARK: - 协议切换视频视图

struct ProtocolSwitchableVideoView: View {
    @ObservedObject var webRTCStreamerClient: WebRTCStreamerClient
    @StateObject private var rtspPlayer = RTSPVideoPlayer()
    @StateObject private var deviceDetector = DeviceTypeDetector.shared
    let camera: CameraDevice

    // 视频协议类型 - 默认使用RTSP协议
    @State private var selectedProtocol: VideoProtocol = .rtsp
    @State private var showProtocolSelector = false
    
    var body: some View {
        ZStack {
            // 视频内容
            Group {
                switch selectedProtocol {
                case .webRTC:
                    WebRTCVideoView(webRTCStreamerClient: webRTCStreamerClient, camera: camera)
                case .rtsp:
                    RTSPVideoView(rtspPlayer: rtspPlayer, camera: camera)
                }
            }
            .ignoresSafeArea(.all)
            
            // 协议切换按钮 - 根据设备类型调整位置
            if deviceDetector.isPhone {
                // iPhone: 左上角，避免与扫描按钮重叠
                VStack {
                    HStack {
                        VStack(spacing: 6) {
                            // 协议切换按钮 - 紧凑设计
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    showProtocolSelector.toggle()
                                }
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: protocolIcon)
                                        .font(.system(size: 12, weight: .medium))
                                    Text(selectedProtocol.displayName)
                                        .font(.system(size: 10, weight: .medium))
                                    Image(systemName: "chevron.down")
                                        .font(.system(size: 8))
                                        .rotationEffect(.degrees(showProtocolSelector ? 180 : 0))
                                }
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.black.opacity(0.7))
                                .foregroundColor(.white)
                                .cornerRadius(16)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(protocolColor, lineWidth: 1)
                                )
                            }

                            // 协议选择器 - 紧凑垂直布局
                            if showProtocolSelector {
                                VStack(spacing: 2) {
                                    ForEach(VideoProtocol.allCases, id: \.self) { videoProtocol in
                                        Button(action: {
                                            switchToProtocol(videoProtocol)
                                            withAnimation(.easeInOut(duration: 0.3)) {
                                                showProtocolSelector = false
                                            }
                                        }) {
                                            HStack(spacing: 6) {
                                                Image(systemName: videoProtocol.icon)
                                                    .font(.system(size: 10))
                                                Text(videoProtocol.displayName)
                                                    .font(.system(size: 9, weight: .medium))
                                                if selectedProtocol == videoProtocol {
                                                    Image(systemName: "checkmark")
                                                        .font(.system(size: 8))
                                                        .foregroundColor(.green)
                                                }
                                            }
                                            .padding(.horizontal, 8)
                                            .padding(.vertical, 3)
                                            .frame(width: 70)
                                            .background(
                                                selectedProtocol == videoProtocol ?
                                                Color.blue.opacity(0.4) : Color.black.opacity(0.7)
                                            )
                                            .foregroundColor(.white)
                                            .cornerRadius(6)
                                        }
                                    }
                                }
                                .padding(.vertical, 3)
                                .background(Color.black.opacity(0.8))
                                .cornerRadius(8)
                                .transition(.opacity.combined(with: .scale(scale: 0.9)))
                            }
                        }
                        .padding(.leading, 16)

                        Spacer()
                    }
                    .padding(.top, 16)

                    Spacer()
                }
            } else {
                // iPad: 右上角，更大的按钮和选择器
                VStack {
                    HStack {
                        Spacer()

                        VStack(spacing: 8) {
                            // 协议切换按钮 - iPad样式
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    showProtocolSelector.toggle()
                                }
                            }) {
                                HStack(spacing: 6) {
                                    Image(systemName: protocolIcon)
                                        .font(.system(size: 14, weight: .medium))
                                    Text(selectedProtocol.displayName)
                                        .font(.system(size: 12, weight: .medium))
                                    Image(systemName: "chevron.down")
                                        .font(.system(size: 10))
                                        .rotationEffect(.degrees(showProtocolSelector ? 180 : 0))
                                }
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.black.opacity(0.7))
                                .foregroundColor(.white)
                                .cornerRadius(20)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(protocolColor, lineWidth: 1)
                                )
                            }

                            // 协议选择器 - iPad样式
                            if showProtocolSelector {
                                VStack(spacing: 4) {
                                    ForEach(VideoProtocol.allCases, id: \.self) { videoProtocol in
                                        Button(action: {
                                            switchToProtocol(videoProtocol)
                                            withAnimation(.easeInOut(duration: 0.3)) {
                                                showProtocolSelector = false
                                            }
                                        }) {
                                            HStack(spacing: 8) {
                                                Image(systemName: videoProtocol.icon)
                                                    .font(.system(size: 12))
                                                Text(videoProtocol.displayName)
                                                    .font(.system(size: 11, weight: .medium))
                                                Spacer()
                                                if selectedProtocol == videoProtocol {
                                                    Image(systemName: "checkmark")
                                                        .font(.system(size: 10))
                                                        .foregroundColor(.green)
                                                }
                                            }
                                            .padding(.horizontal, 10)
                                            .padding(.vertical, 6)
                                            .frame(width: 120)
                                            .background(
                                                selectedProtocol == videoProtocol ?
                                                Color.blue.opacity(0.3) : Color.black.opacity(0.7)
                                            )
                                            .foregroundColor(.white)
                                            .cornerRadius(8)
                                        }
                                    }
                                }
                                .padding(.vertical, 4)
                                .background(Color.black.opacity(0.8))
                                .cornerRadius(12)
                                .transition(.opacity.combined(with: .scale(scale: 0.9)))
                            }
                        }
                        .padding(.trailing, 16)
                    }
                    .padding(.top, 16)

                    Spacer()
                }
            }
        }
        .onDisappear {
            // 清理资源
            rtspPlayer.disconnect()
        }
    }
    
    // MARK: - Private Methods
    
    private var protocolIcon: String {
        switch selectedProtocol {
        case .webRTC:
            return webRTCStreamerClient.isConnected ? "wifi" : "wifi.slash"
        case .rtsp:
            return rtspPlayer.isConnected ? "video.fill" : "video.slash"
        }
    }
    
    private var protocolColor: Color {
        switch selectedProtocol {
        case .webRTC:
            return webRTCStreamerClient.isConnected ? .green : .red
        case .rtsp:
            return rtspPlayer.isConnected ? .green : .red
        }
    }
    
    private func switchToProtocol(_ videoProtocol: VideoProtocol) {
        print("[ProtocolSwitchableVideoView] 🔄 Switching to protocol: \(videoProtocol)")

        // 记录切换时间用于延迟对比
        let switchStartTime = Date()

        selectedProtocol = videoProtocol

        switch videoProtocol {
        case .webRTC:
            // 断开RTSP连接
            rtspPlayer.disconnect()

            // 确保WebRTC连接
            if !webRTCStreamerClient.isConnected {
                // 这里可以触发WebRTC重连
                print("[ProtocolSwitchableVideoView] 🔄 WebRTC not connected, may need to reconnect")
            }

        case .rtsp:
            // 开始RTSP连接
            Task {
                await rtspPlayer.connect(to: camera.ipAddress)

                let switchEndTime = Date()
                let switchLatency = switchEndTime.timeIntervalSince(switchStartTime) * 1000
                print("[ProtocolSwitchableVideoView] ⏱️ Protocol switch latency: \(String(format: "%.1f", switchLatency))ms")
            }
        }
    }
}

// MARK: - 视频协议枚举

enum VideoProtocol: String, CaseIterable {
    case webRTC = "webrtc"
    case rtsp = "rtsp"
    
    var displayName: String {
        switch self {
        case .webRTC:
            return "WebRTC"
        case .rtsp:
            return "RTSP"
        }
    }
    
    var icon: String {
        switch self {
        case .webRTC:
            return "wifi"
        case .rtsp:
            return "video.fill"
        }
    }
    
    var description: String {
        switch self {
        case .webRTC:
            return "通过WebRTC Streamer转换的视频流"
        case .rtsp:
            return "直连RTSP流，延迟更低"
        }
    }
}
