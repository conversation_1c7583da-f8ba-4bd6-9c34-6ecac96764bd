//
//  ScanControlComponents.swift
//  wbCamer
//
//  Created by AI Assistant on 2025/6/30.
//

import SwiftUI

// MARK: - 扫描切换按钮（iPhone专用）

struct ScanToggleButton: View {
    @ObservedObject private var serviceDiscovery = EagleServiceDiscovery.shared
    @State private var showingScanPanel = false

    var body: some View {
        // 扫描按钮
        Button(action: {
            withAnimation(.easeInOut(duration: 0.3)) {
                showingScanPanel.toggle()
            }
        }) {
            Image(systemName: showingScanPanel ? "xmark.circle.fill" : "magnifyingglass.circle.fill")
                .font(.system(size: 32))
                .foregroundColor(.white)
                .background(
                    Circle()
                        .fill(Color.black.opacity(0.6))
                        .frame(width: 44, height: 44)
                )
        }
        // 使用 fullScreenCover 确保扫描面板在最高层级
        .fullScreenCover(isPresented: $showingScanPanel) {
            ScanPanelOverlay(isPresented: $showingScanPanel)
        }
    }
}

// MARK: - 扫描面板覆盖层
struct ScanPanelOverlay: View {
    @Binding var isPresented: Bool

    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.3)
                .ignoresSafeArea(.all)
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isPresented = false
                    }
                }

            // 居中的扫描面板
            VStack(spacing: 16) {
                // 标题
                HStack {
                    Text("设备扫描")
                        .font(.headline)
                        .foregroundColor(.white)
                    Spacer()
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isPresented = false
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.white)
                    }
                }

                // 扫描控制
                ScanControlSection()

                // 设备列表 - 居中显示
                HStack {
                    Spacer()
                    DeviceListSection()
                        .frame(maxHeight: 200)
                        .frame(maxWidth: 350)  // 限制最大宽度，实现居中效果
                    Spacer()
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.black.opacity(0.95))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
            .frame(width: 400)
            .transition(.asymmetric(
                insertion: .scale.combined(with: .opacity),
                removal: .scale.combined(with: .opacity)
            ))
        }
        .background(Color.clear)
    }
}

// MARK: - 扫描控制区域

struct ScanControlSection: View {
    @ObservedObject private var serviceDiscovery = EagleServiceDiscovery.shared
    @StateObject private var networkPermissionManager = NetworkPermissionManager()
    
    var body: some View {
        VStack(spacing: 12) {
            // 网络权限状态
            if networkPermissionManager.hasCompletedInitialCheck && !networkPermissionManager.hasLocalNetworkPermission {
                HStack {
                    Image(systemName: "wifi.exclamationmark")
                        .foregroundColor(.orange)
                    Text("需要网络权限")
                        .font(.caption)
                        .foregroundColor(.orange)
                    Spacer()
                    Button("检查") {
                        networkPermissionManager.requestPermissionIfNeeded()
                    }
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange.opacity(0.2))
                    .cornerRadius(4)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }
            
            // 扫描状态和控制
            HStack(spacing: 16) {
                // 手动扫描按钮
                Button(action: {
                    if !networkPermissionManager.hasLocalNetworkPermission {
                        networkPermissionManager.checkLocalNetworkPermission()
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                            if networkPermissionManager.hasLocalNetworkPermission {
                                serviceDiscovery.startManualScan()
                            }
                        }
                    } else {
                        serviceDiscovery.startManualScan()
                    }
                }) {
                    HStack(spacing: 8) {
                        if serviceDiscovery.isScanning {
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        } else {
                            Image(systemName: "magnifyingglass")
                        }
                        Text(serviceDiscovery.isScanning ? "扫描中..." : "扫描设备")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(networkPermissionManager.hasLocalNetworkPermission ? Color.blue : Color.gray)
                    )
                }
                .disabled(serviceDiscovery.isScanning)
                
                // 清除结果按钮
                Button(action: {
                    serviceDiscovery.clearResults()
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "trash")
                        Text("清除")
                    }
                    .font(.subheadline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.red.opacity(0.8))
                    )
                }
            }
            
            // 扫描状态信息
            if let lastScanTime = serviceDiscovery.lastScanTime {
                Text("上次扫描: \(formatTime(lastScanTime))")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        return formatter.string(from: date)
    }
}

// MARK: - 设备列表区域

struct DeviceListSection: View {
    @ObservedObject private var serviceDiscovery = EagleServiceDiscovery.shared

    var body: some View {
        VStack(spacing: 12) {
            // 标题 - 居中显示
            Text("发现的设备 (\(serviceDiscovery.discoveredIPs.count))")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity)

            // 设备列表 - 居中显示
            if serviceDiscovery.discoveredIPs.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "wifi.slash")
                        .font(.system(size: 24))
                        .foregroundColor(.gray)
                    Text("未发现设备")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Text("请确保设备在同一局域网内")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            } else {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(serviceDiscovery.discoveredIPs, id: \.self) { ip in
                            CompactIPAddressRow(ipAddress: ip)
                        }
                    }
                    .padding(.horizontal, 8)  // 添加水平内边距
                }
            }
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 紧凑设备列表区域（iPad专用）

struct CompactDeviceListSection: View {
    @ObservedObject private var serviceDiscovery = EagleServiceDiscovery.shared

    var body: some View {
        VStack(spacing: 12) {
            // 标题 - 居中显示
            Text("设备 (\(serviceDiscovery.discoveredIPs.count))")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)

            // 设备列表 - 完全居中显示
            if serviceDiscovery.discoveredIPs.isEmpty {
                VStack(spacing: 6) {
                    Image(systemName: "wifi.slash")
                        .font(.system(size: 18))
                        .foregroundColor(.gray)
                    Text("未发现设备")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .frame(height: 60)
                .frame(maxWidth: .infinity)
            } else {
                // 垂直列表，完全居中显示
                ScrollView(.vertical, showsIndicators: false) {
                    VStack(spacing: 8) {
                        ForEach(serviceDiscovery.discoveredIPs, id: \.self) { ip in
                            CompactIPAddressRow(ipAddress: ip)
                                .frame(maxWidth: 280)  // 限制最大宽度，确保居中
                        }
                    }
                    .frame(maxWidth: .infinity)  // 确保VStack居中
                }
                .frame(maxHeight: 120)
                .frame(maxWidth: .infinity)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 20)  // 添加水平内边距
    }
}

// MARK: - 紧凑IP地址行

struct CompactIPAddressRow: View {
    let ipAddress: String
    @StateObject private var cameraManager = CameraManager.shared

    var body: some View {
        Button(action: {
            connectToCamera()
        }) {
            // 居中显示IP地址的布局
            VStack(spacing: 6) {
                HStack(spacing: 8) {
                    Image(systemName: "network")
                        .foregroundColor(.green)
                        .font(.system(size: 16))

                    Text(ipAddress)
                        .font(.system(.subheadline, design: .monospaced))
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    Image(systemName: "play.circle")
                        .foregroundColor(.blue)
                        .font(.system(size: 16))
                }
                .frame(maxWidth: .infinity)  // 确保内容居中
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func connectToCamera() {
        let cleanIP = ipAddress.components(separatedBy: ":").first ?? ipAddress
        let camera = CameraDevice(
            name: "ZCam Camera",
            ipAddress: cleanIP,
            port: 80
        )
        cameraManager.connect(to: camera)
    }
}

// MARK: - 迷你IP地址行（iPad专用）

struct MiniIPAddressRow: View {
    let ipAddress: String
    @StateObject private var cameraManager = CameraManager.shared
    
    var body: some View {
        Button(action: {
            connectToCamera()
        }) {
            HStack(spacing: 6) {
                Image(systemName: "network")
                    .foregroundColor(.green)
                    .font(.system(size: 12))
                
                Text(ipAddress.components(separatedBy: ":").first ?? ipAddress)
                    .font(.system(.caption, design: .monospaced))
                    .foregroundColor(.white)
                    .lineLimit(1)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.blue.opacity(0.8))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func connectToCamera() {
        let cleanIP = ipAddress.components(separatedBy: ":").first ?? ipAddress
        let camera = CameraDevice(
            name: "ZCam Camera",
            ipAddress: cleanIP,
            port: 80
        )
        cameraManager.connect(to: camera)
    }
}

// MARK: - 设备扫描覆盖层（已废弃）
// DeviceScanOverlay已被移除，现在使用ScanToggleButton处理iPhone的扫描控制
