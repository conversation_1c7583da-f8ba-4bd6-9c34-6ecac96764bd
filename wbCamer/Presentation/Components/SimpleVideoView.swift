//
//  SimpleVideoView.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI
import WebRTC
import Metal

// MARK: - 简化的视频显示组件

struct SimpleVideoView: View {
    @EnvironmentObject var webRTCStreamerClient: WebRTCStreamerClient
    @State private var showControls = true
    
    var body: some View {
        ZStack {
            // 背景颜色 - 应该总是可见的
            Color.purple
                .ignoresSafeArea()
                .overlay(
                    Text("🎥 SIMPLE VIDEO VIEW 🎥")
                        .font(.title)
                        .foregroundColor(.white)
                        .background(Color.black.opacity(0.7))
                        .padding()
                )
            
            // 视频渲染器
            if let videoTrack = webRTCStreamerClient.remoteVideoTrack {
                SimpleVideoRenderer(videoTrack: videoTrack)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.orange) // 橙色背景用于调试
                    .onTapGesture {
                        withAnimation {
                            showControls.toggle()
                        }
                    }
            } else {
                // 无视频时的占位符
                VStack {
                    Image(systemName: "video.slash")
                        .font(.system(size: 64))
                        .foregroundColor(.white)
                    
                    Text("等待视频流...")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.top)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black)
            }
            
            // 控制界面
            if showControls {
                VStack {
                    HStack {
                        // 连接状态指示器
                        HStack {
                            Circle()
                                .fill(webRTCStreamerClient.isConnected ? Color.green : Color.red)
                                .frame(width: 12, height: 12)
                            
                            Text(webRTCStreamerClient.isConnected ? "已连接" : "未连接")
                                .font(.caption)
                                .foregroundColor(.white)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.black.opacity(0.7))
                        .cornerRadius(16)
                        
                        Spacer()
                        
                        // 全屏按钮
                        Button(action: {
                            // 全屏功能
                        }) {
                            Image(systemName: "arrow.up.left.and.arrow.down.right")
                                .foregroundColor(.white)
                                .padding(8)
                                .background(Color.black.opacity(0.7))
                                .clipShape(Circle())
                        }
                    }
                    .padding()
                    
                    Spacer()
                    
                    // 底部控制栏
                    HStack {
                        Button(action: {
                            // 录制功能
                        }) {
                            Image(systemName: "record.circle")
                                .font(.title2)
                                .foregroundColor(.white)
                                .padding()
                                .background(Color.red.opacity(0.8))
                                .clipShape(Circle())
                        }
                        
                        Spacer()
                        
                        Button(action: {
                            // 拍照功能
                        }) {
                            Image(systemName: "camera")
                                .font(.title2)
                                .foregroundColor(.white)
                                .padding()
                                .background(Color.blue.opacity(0.8))
                                .clipShape(Circle())
                        }
                    }
                    .padding()
                }
            }
        }
        .onAppear {
            print("[SimpleVideoView] SimpleVideoView appeared")
            print("[SimpleVideoView] Current videoTrack: \(webRTCStreamerClient.remoteVideoTrack != nil ? "exists" : "nil")")
        }
    }
}

// MARK: - 简化的视频渲染器

struct SimpleVideoRenderer: UIViewRepresentable {
    let videoTrack: RTCVideoTrack
    
    func makeUIView(context: Context) -> RTCMTLVideoView {
        print("[SimpleVideoRenderer] Creating RTCMTLVideoView")
        
        let videoView = RTCMTLVideoView()
        
        // 基本配置
        videoView.backgroundColor = UIColor.cyan // 青色背景用于调试
        videoView.contentMode = .scaleAspectFit
        videoView.isOpaque = false
        videoView.clipsToBounds = false
        
        // 添加明显的边框
        videoView.layer.borderColor = UIColor.magenta.cgColor
        videoView.layer.borderWidth = 5.0
        
        print("[SimpleVideoRenderer] RTCMTLVideoView created with cyan background and magenta border")
        
        return videoView
    }
    
    func updateUIView(_ uiView: RTCMTLVideoView, context: Context) {
        print("[SimpleVideoRenderer] updateUIView called")
        print("[SimpleVideoRenderer] VideoTrack: \(videoTrack)")
        print("[SimpleVideoRenderer] UIView frame: \(uiView.frame)")
        print("[SimpleVideoRenderer] UIView bounds: \(uiView.bounds)")
        print("[SimpleVideoRenderer] UIView superview: \(uiView.superview != nil ? "exists" : "nil")")
        print("[SimpleVideoRenderer] UIView window: \(uiView.window != nil ? "exists" : "nil")")
        
        // 添加视频轨道
        videoTrack.add(uiView)
        print("[SimpleVideoRenderer] Video track added to renderer")
        
        // 强制设置尺寸
        DispatchQueue.main.async {
            if let window = UIApplication.shared.connectedScenes
                .compactMap({ $0 as? UIWindowScene })
                .first?.windows.first {
                
                let screenSize = window.bounds.size
                let targetFrame = CGRect(
                    x: 0,
                    y: 0,
                    width: screenSize.width * 0.9,
                    height: screenSize.height * 0.6
                )
                
                uiView.frame = targetFrame
                print("[SimpleVideoRenderer] Forced frame to: \(targetFrame)")
                
                // 强制刷新
                uiView.setNeedsDisplay()
                uiView.setNeedsLayout()
                uiView.layoutIfNeeded()
                
                // 延迟检查
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    print("[SimpleVideoRenderer] 🔍 Delayed check:")
                    print("[SimpleVideoRenderer] - frame: \(uiView.frame)")
                    print("[SimpleVideoRenderer] - bounds: \(uiView.bounds)")
                    print("[SimpleVideoRenderer] - window: \(uiView.window != nil ? "exists" : "nil")")
                    print("[SimpleVideoRenderer] - superview: \(uiView.superview != nil ? "exists" : "nil")")
                    print("[SimpleVideoRenderer] - backgroundColor: \(uiView.backgroundColor?.description ?? "nil")")
                    print("[SimpleVideoRenderer] - isHidden: \(uiView.isHidden)")
                    print("[SimpleVideoRenderer] - alpha: \(uiView.alpha)")
                }
            }
        }
    }
}

// MARK: - Preview

struct SimpleVideoView_Previews: PreviewProvider {
    static var previews: some View {
        SimpleVideoView()
            .environmentObject(WebRTCStreamerClient())
    }
}
