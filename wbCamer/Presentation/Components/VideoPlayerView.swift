//
//  VideoPlayerView.swift
//  wbCamer
//
//  Created by Assistant on 2024-12-19.
//

import SwiftUI
import WebRTC
import MetalKit

/// WebRTC视频播放器视图
struct VideoPlayerView: UIViewRepresentable {
    let videoTrack: RTCVideoTrack?
    let contentMode: UIView.ContentMode
    
    init(videoTrack: RTCVideoTrack?, contentMode: UIView.ContentMode = .scaleAspectFit) {
        self.videoTrack = videoTrack
        self.contentMode = contentMode
        print("[VideoPlayerView] Initialized with videoTrack: \(String(describing: videoTrack))")
    }
    
    func makeUIView(context: Context) -> RTCMTLVideoView {
        print("[VideoPlayerView] Creating RTCMTLVideoView")
        let videoView = RTCMTLVideoView(frame: .zero)
        videoView.contentMode = contentMode
        videoView.delegate = context.coordinator

        // 确保视频视图可见
        videoView.backgroundColor = UIColor.red  // 改为红色以便调试
        videoView.isOpaque = false  // 改为非不透明以便调试
        videoView.clipsToBounds = false  // 改为不裁剪以便调试
        videoView.isHidden = false
        videoView.alpha = 1.0

        // 添加边框以便调试
        videoView.layer.borderColor = UIColor.green.cgColor
        videoView.layer.borderWidth = 2.0

        // 强制启用 Metal 渲染
        print("[VideoPlayerView] Metal video view created successfully")
        // 确保 Metal 设备可用
        if let metalDevice = MTLCreateSystemDefaultDevice() {
            print("[VideoPlayerView] Metal device available: \(metalDevice.name)")
        } else {
            print("[VideoPlayerView] ❌ Metal device not available!")
        }

        // 设置自动布局约束以确保视图有正确的尺寸
        videoView.translatesAutoresizingMaskIntoConstraints = false
        videoView.setContentHuggingPriority(.defaultLow, for: .horizontal)
        videoView.setContentHuggingPriority(.defaultLow, for: .vertical)
        videoView.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
        videoView.setContentCompressionResistancePriority(.defaultLow, for: .vertical)

        print("[VideoPlayerView] RTCMTLVideoView created with contentMode: \(contentMode)")
        print("[VideoPlayerView] RTCMTLVideoView properties - backgroundColor: \(videoView.backgroundColor?.description ?? "nil"), isHidden: \(videoView.isHidden), alpha: \(videoView.alpha)")
        // 强制设置初始尺寸并确保视图在 window 中
        DispatchQueue.main.async {
            print("[VideoPlayerView] Setting initial frame size")
            if let window = UIApplication.shared.connectedScenes
                .compactMap({ $0 as? UIWindowScene })
                .first?.windows.first {
                let screenSize = window.bounds.size
                let targetSize = CGSize(width: screenSize.width * 0.8, height: screenSize.height * 0.6)
                videoView.frame = CGRect(origin: .zero, size: targetSize)
                print("[VideoPlayerView] Set initial frame to: \(videoView.frame)")

                // 强制检查视图是否在 window 中
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    print("[VideoPlayerView] 🔍 Checking window connection after delay:")
                    print("[VideoPlayerView] - window: \(videoView.window != nil ? "exists" : "nil")")
                    print("[VideoPlayerView] - superview: \(videoView.superview != nil ? "exists" : "nil")")
                    print("[VideoPlayerView] - frame: \(videoView.frame)")
                    print("[VideoPlayerView] - isHidden: \(videoView.isHidden)")
                    print("[VideoPlayerView] - alpha: \(videoView.alpha)")

                    if videoView.window == nil {
                        print("[VideoPlayerView] ⚠️ Video view is not in window hierarchy!")
                        // 尝试强制刷新视图层级
                        if let superview = videoView.superview {
                            superview.setNeedsLayout()
                            superview.layoutIfNeeded()
                            print("[VideoPlayerView] 🔄 Forced superview layout refresh")
                        }
                    }
                }
            }
        }

        return videoView
    }
    
    func updateUIView(_ uiView: RTCMTLVideoView, context: Context) {
        // 强制设置视图尺寸 - 修复父视图尺寸问题
        DispatchQueue.main.async {
            if let superview = uiView.superview {
                print("[VideoPlayerView] Superview frame: \(superview.frame)")
                print("[VideoPlayerView] Superview bounds: \(superview.bounds)")

                // 如果父视图尺寸为零，强制设置一个合理的尺寸
                if superview.frame.size == .zero {
                    print("[VideoPlayerView] Superview has zero size, forcing layout")
                    if let window = UIApplication.shared.connectedScenes
                        .compactMap({ $0 as? UIWindowScene })
                        .first?.windows.first {
                        let screenSize = window.bounds.size
                        let targetSize = CGSize(width: screenSize.width * 0.9, height: screenSize.height * 0.6)
                        superview.frame = CGRect(origin: .zero, size: targetSize)
                        print("[VideoPlayerView] Set superview frame to: \(superview.frame)")
                    }
                }

                // 确保视频视图填充父视图
                uiView.frame = superview.bounds
                uiView.setNeedsLayout()
                uiView.layoutIfNeeded()
                print("[VideoPlayerView] Updated RTCMTLVideoView frame to: \(uiView.frame)")
            }
        }

        // 检查是否需要更新视频轨道
        let currentTrack = context.coordinator.currentVideoTrack

        // 如果视频轨道没有变化，则不需要更新
        if currentTrack === videoTrack {
            print("[VideoPlayerView] Video track unchanged, skipping update")
            return
        }

        // 先移除之前的视频轨道
        context.coordinator.removeVideoTrack(from: uiView)

        // 添加新的视频轨道
        if let videoTrack = videoTrack {
            print("[VideoPlayerView] Adding video track to renderer: \(videoTrack)")
            print("[VideoPlayerView] Video track enabled: \(videoTrack.isEnabled)")
            print("[VideoPlayerView] Video track readyState: \(videoTrack.readyState)")
            print("[VideoPlayerView] Video track kind: \(videoTrack.kind)")
            print("[VideoPlayerView] Video track trackId: \(videoTrack.trackId)")
            print("[VideoPlayerView] RTCMTLVideoView frame: \(uiView.frame)")
            print("[VideoPlayerView] RTCMTLVideoView bounds: \(uiView.bounds)")
            print("[VideoPlayerView] RTCMTLVideoView superview: \(String(describing: uiView.superview))")

            videoTrack.add(uiView)
            context.coordinator.setCurrentVideoTrack(videoTrack)
            print("[VideoPlayerView] Video track successfully added to RTCMTLVideoView")

            // 强制触发视图更新
            DispatchQueue.main.async {
                uiView.setNeedsDisplay()
                uiView.setNeedsLayout()
                uiView.layoutIfNeeded()
                print("[VideoPlayerView] Forced view refresh after adding video track")

                // 检查视图层级
                print("[VideoPlayerView] View hierarchy check:")
                print("[VideoPlayerView] - isHidden: \(uiView.isHidden)")
                print("[VideoPlayerView] - alpha: \(uiView.alpha)")
                print("[VideoPlayerView] - backgroundColor: \(uiView.backgroundColor?.description ?? "nil")")
                print("[VideoPlayerView] - superview: \(uiView.superview != nil ? "exists" : "nil")")
                print("[VideoPlayerView] - window: \(uiView.window != nil ? "exists" : "nil")")

                // 检查 Metal 层
                print("[VideoPlayerView] Metal layer check:")
                print("[VideoPlayerView] - layer: \(uiView.layer)")
                print("[VideoPlayerView] - layer.bounds: \(uiView.layer.bounds)")
                print("[VideoPlayerView] - layer.isHidden: \(uiView.layer.isHidden)")
            }

            // 再次检查并强制设置尺寸
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                if let superview = uiView.superview {
                    print("[VideoPlayerView] Post-track-add: Checking layout")
                    print("[VideoPlayerView] Post-track-add: Superview frame: \(superview.frame)")
                    print("[VideoPlayerView] Post-track-add: VideoView frame: \(uiView.frame)")

                    // 如果父视图或视频视图尺寸仍然有问题，强制修复
                    if superview.frame.size == .zero || uiView.frame.size == .zero {
                        print("[VideoPlayerView] Post-track-add: Forcing layout fix")
                        if let window = UIApplication.shared.connectedScenes
                            .compactMap({ $0 as? UIWindowScene })
                            .first?.windows.first {
                            let screenSize = window.bounds.size
                            let targetSize = CGSize(width: screenSize.width * 0.9, height: screenSize.height * 0.6)

                            if superview.frame.size == .zero {
                                superview.frame = CGRect(origin: .zero, size: targetSize)
                                print("[VideoPlayerView] Post-track-add: Fixed superview frame to: \(superview.frame)")
                            }

                            uiView.frame = superview.bounds
                            uiView.setNeedsLayout()
                            uiView.layoutIfNeeded()
                            print("[VideoPlayerView] Post-track-add: Fixed VideoView frame to: \(uiView.frame)")
                        }
                    }
                }
            }
        } else {
            print("[VideoPlayerView] No video track to add")
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
    
    class Coordinator: NSObject, RTCVideoViewDelegate {
        private var _currentVideoTrack: RTCVideoTrack?

        var currentVideoTrack: RTCVideoTrack? {
            return _currentVideoTrack
        }

        func removeVideoTrack(from videoView: RTCMTLVideoView) {
            if let track = _currentVideoTrack {
                print("[VideoPlayerView] Removing video track from renderer")
                track.remove(videoView)
                _currentVideoTrack = nil
            }
        }

        func setCurrentVideoTrack(_ track: RTCVideoTrack) {
            _currentVideoTrack = track
        }

        // MARK: - RTCVideoViewDelegate

        func videoView(_ videoView: RTCVideoRenderer, didChangeVideoSize size: CGSize) {
            print("[VideoPlayerView] 🎥 Video size changed: \(size)")

            // 确保视频视图有正确的尺寸
            DispatchQueue.main.async {
                if let mtlVideoView = videoView as? RTCMTLVideoView {
                    print("[VideoPlayerView] VideoView frame when size changed: \(mtlVideoView.frame)")
                    print("[VideoPlayerView] VideoView bounds when size changed: \(mtlVideoView.bounds)")

                    // 强制刷新视图
                    mtlVideoView.setNeedsDisplay()
                    mtlVideoView.setNeedsLayout()
                    mtlVideoView.layoutIfNeeded()

                    // 检查视图状态
                    print("[VideoPlayerView] 🔍 Video view state after size change:")
                    print("[VideoPlayerView] - frame: \(mtlVideoView.frame)")
                    print("[VideoPlayerView] - bounds: \(mtlVideoView.bounds)")
                    print("[VideoPlayerView] - isHidden: \(mtlVideoView.isHidden)")
                    print("[VideoPlayerView] - alpha: \(mtlVideoView.alpha)")
                    print("[VideoPlayerView] - backgroundColor: \(mtlVideoView.backgroundColor?.description ?? "nil")")
                    print("[VideoPlayerView] - layer.isHidden: \(mtlVideoView.layer.isHidden)")
                    print("[VideoPlayerView] - layer.opacity: \(mtlVideoView.layer.opacity)")

                    if mtlVideoView.frame.size == .zero, let superview = mtlVideoView.superview {
                        print("[VideoPlayerView] Size change: Forcing view frame update")
                        mtlVideoView.frame = superview.bounds
                        mtlVideoView.setNeedsLayout()
                        mtlVideoView.layoutIfNeeded()
                        print("[VideoPlayerView] Size change: Updated frame to: \(mtlVideoView.frame)")
                    }

                    // 强制重绘
                    mtlVideoView.setNeedsDisplay()
                    print("[VideoPlayerView] 🎨 Forced redraw after video size change")
                }
            }
        }
    }
}

/// 带有控制界面的视频播放器 (支持 WebRTC Streamer)
struct VideoPlayerControlView: View {
    @ObservedObject var webRTCStreamerClient: WebRTCStreamerClient
    @State private var isFullscreen = false
    @State private var showControls = true
    @State private var controlsTimer: Timer?

    var body: some View {
        let _ = print("[VideoPlayerControlView] Body refresh - remoteVideoTrack: \(webRTCStreamerClient.remoteVideoTrack != nil), isConnected: \(webRTCStreamerClient.isConnected)")

        ZStack {
            // 测试用的背景 - 应该能看到这个
            Rectangle()
                .fill(Color.yellow)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .overlay(
                    Text("🎥 VIDEO AREA 🎥")
                        .font(.title)
                        .foregroundColor(.black)
                )

            // 视频播放器 - 使用 id 来防止不必要的重建
            VideoPlayerView(videoTrack: webRTCStreamerClient.remoteVideoTrack)
                .id("video-player-main") // 固定 ID 防止重建
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.blue) // 改为蓝色以便调试
                .clipped() // 不裁剪
                .allowsHitTesting(true) // 允许触摸
                .onTapGesture {
                    toggleControls()
                }
                .onChange(of: webRTCStreamerClient.remoteVideoTrack) { oldValue, newValue in
                    print("[VideoPlayerControlView] Video track changed from \(String(describing: oldValue)) to \(String(describing: newValue))")
                }
                .onAppear {
                    print("[VideoPlayerControlView] VideoPlayerControlView appeared")
                    print("[VideoPlayerControlView] Current remoteVideoTrack: \(String(describing: webRTCStreamerClient.remoteVideoTrack))")
                }

            // 连接状态覆盖层
            if !webRTCStreamerClient.isConnected {
                ConnectionStatusOverlay(connectionState: webRTCStreamerClient.connectionState)
            }

            // 控制界面
            if showControls {
                VideoControlsOverlay(
                    webRTCStreamerClient: webRTCStreamerClient,
                    isFullscreen: $isFullscreen,
                    onHideControls: {
                        hideControls()
                    }
                )
            }
        }
        .background(Color.black)
        .onAppear {
            startControlsTimer()
        }
        .onDisappear {
            stopControlsTimer()
        }
    }
    
    private func toggleControls() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showControls.toggle()
        }
        
        if showControls {
            startControlsTimer()
        } else {
            stopControlsTimer()
        }
    }
    
    private func hideControls() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showControls = false
        }
        stopControlsTimer()
    }
    
    private func startControlsTimer() {
        stopControlsTimer()
        controlsTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            hideControls()
        }
    }
    
    private func stopControlsTimer() {
        controlsTimer?.invalidate()
        controlsTimer = nil
    }
}

/// 连接状态覆盖层
struct ConnectionStatusOverlay: View {
    let connectionState: RTCPeerConnectionState
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.7)
            
            VStack(spacing: 16) {
                // 状态图标
                Image(systemName: statusIcon)
                    .font(.system(size: 48))
                    .foregroundColor(statusColor)
                
                // 状态文本
                Text(statusText)
                    .font(.headline)
                    .foregroundColor(.white)
                
                // 加载指示器
                if isConnecting {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                }
            }
        }
    }
    
    private var statusIcon: String {
        switch connectionState {
        case .new, .connecting:
            return "wifi.circle"
        case .connected:
            return "checkmark.circle.fill"
        case .disconnected:
            return "wifi.slash"
        case .failed:
            return "exclamationmark.triangle.fill"
        case .closed:
            return "xmark.circle.fill"
        @unknown default:
            return "questionmark.circle"
        }
    }
    
    private var statusColor: Color {
        switch connectionState {
        case .new, .connecting:
            return .blue
        case .connected:
            return .green
        case .disconnected, .closed:
            return .gray
        case .failed:
            return .red
        @unknown default:
            return .gray
        }
    }
    
    private var statusText: String {
        switch connectionState {
        case .new:
            return "准备连接"
        case .connecting:
            return "正在连接..."
        case .connected:
            return "已连接"
        case .disconnected:
            return "连接断开"
        case .failed:
            return "连接失败"
        case .closed:
            return "连接已关闭"
        @unknown default:
            return "未知状态"
        }
    }
    
    private var isConnecting: Bool {
        connectionState == .connecting
    }
}

/// 视频控制覆盖层 (支持 WebRTC Streamer)
struct VideoControlsOverlay: View {
    @ObservedObject var webRTCStreamerClient: WebRTCStreamerClient
    @Binding var isFullscreen: Bool
    let onHideControls: () -> Void
    
    var body: some View {
        VStack {
            // 顶部控制栏
            HStack {
                // 连接状态指示器
                HStack(spacing: 8) {
                    Circle()
                        .fill(webRTCStreamerClient.isConnected ? Color.green : Color.red)
                        .frame(width: 8, height: 8)

                    Text(webRTCStreamerClient.isConnected ? "已连接" : "未连接")
                        .font(.caption)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.black.opacity(0.6))
                .cornerRadius(16)
                
                Spacer()
                
                // 全屏按钮
                Button(action: {
                    isFullscreen.toggle()
                }) {
                    Image(systemName: isFullscreen ? "arrow.down.right.and.arrow.up.left" : "arrow.up.left.and.arrow.down.right")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(8)
                        .background(Color.black.opacity(0.6))
                        .clipShape(Circle())
                }
            }
            .padding(.horizontal)
            .padding(.top)
            
            Spacer()
            
            // 底部控制栏
            HStack {
                // 断开连接按钮
                Button(action: {
                    webRTCStreamerClient.disconnect()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "wifi.slash")
                        Text("断开连接")
                    }
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color.red.opacity(0.8))
                    .cornerRadius(20)
                }
                
                Spacer()
                
                // 质量信息
                if webRTCStreamerClient.isConnected {
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("WebRTC")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.8))
                        
                        HStack(spacing: 4) {
                            Image(systemName: "wifi")
                                .font(.caption2)
                            Text(iceConnectionStateText)
                                .font(.caption2)
                        }
                        .foregroundColor(.white.opacity(0.8))
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.black.opacity(0.6))
                    .cornerRadius(12)
                }
            }
            .padding(.horizontal)
            .padding(.bottom)
        }
        .background(
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color.black.opacity(0.6), location: 0),
                    .init(color: Color.clear, location: 0.3),
                    .init(color: Color.clear, location: 0.7),
                    .init(color: Color.black.opacity(0.6), location: 1)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    private var iceConnectionStateText: String {
        switch webRTCStreamerClient.iceConnectionState {
        case .new:
            return "新建"
        case .checking:
            return "检查中"
        case .connected:
            return "已连接"
        case .completed:
            return "完成"
        case .failed:
            return "失败"
        case .disconnected:
            return "断开"
        case .closed:
            return "关闭"
        case .count:
            return "计数"
        @unknown default:
            return "未知"
        }
    }
}

// MARK: - Preview

struct VideoPlayerView_Previews: PreviewProvider {
    static var previews: some View {
        VideoPlayerControlView(webRTCStreamerClient: WebRTCStreamerClient())
            .preferredColorScheme(.dark)
    }
}