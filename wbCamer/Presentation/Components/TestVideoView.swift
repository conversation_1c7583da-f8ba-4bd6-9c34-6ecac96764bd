//
//  TestVideoView.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI
import UIKit
import WebRTC

// MARK: - 极简测试视频视图

struct TestVideoView: View {
    let webRTCStreamerClient: WebRTCStreamerClient
    
    var body: some View {
        ZStack {
            // 明显的背景色 - 应该总是可见
            Color.red
                .ignoresSafeArea(.all)
            
            // 大号文字标识
            VStack {
                Text("🔴 TEST VIDEO VIEW 🔴")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.black.opacity(0.8))
                    .cornerRadius(12)
                
                Text("如果你能看到这个红色背景和文字，说明视图显示正常")
                    .font(.title2)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .padding()
                    .background(Color.black.opacity(0.8))
                    .cornerRadius(8)
                
                Spacer()
                
                // 视频容器
                TestVideoContainer(webRTCStreamerClient: webRTCStreamerClient)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.blue)
                    .cornerRadius(12)
                    .padding()
                
                Spacer()
            }
        }
        .onAppear {
            print("[TestVideoView] 🔴 TestVideoView appeared - THIS SHOULD BE VISIBLE!")
        }
    }
}

// MARK: - 极简视频容器

struct TestVideoContainer: UIViewControllerRepresentable {
    let webRTCStreamerClient: WebRTCStreamerClient

    func makeUIViewController(context: Context) -> TestVideoController {
        print("[TestVideoContainer] 🔴 Creating TestVideoController")
        let controller = TestVideoController()
        controller.webRTCStreamerClient = webRTCStreamerClient

        // 强制触发视图生命周期
        DispatchQueue.main.async {
            print("[TestVideoContainer] 🔴 Forcing view lifecycle")
            controller.loadViewIfNeeded()
            controller.viewWillAppear(false)
            controller.viewDidAppear(false)
        }

        return controller
    }

    func updateUIViewController(_ uiViewController: TestVideoController, context: Context) {
        print("[TestVideoContainer] 🔴 updateUIViewController called")
        uiViewController.updateVideoTrack(webRTCStreamerClient.remoteVideoTrack)

        // 再次强制触发生命周期（如果需要）
        DispatchQueue.main.async {
            if !uiViewController.isViewLoaded || uiViewController.view.window == nil {
                print("[TestVideoContainer] 🔴 View not properly loaded, forcing lifecycle again")
                uiViewController.loadViewIfNeeded()
                uiViewController.viewWillAppear(false)
                uiViewController.viewDidAppear(false)
            }
        }
    }
}

// MARK: - 极简视频控制器

class TestVideoController: UIViewController {
    var webRTCStreamerClient: WebRTCStreamerClient?
    private var videoView: RTCMTLVideoView?
    private var currentVideoTrack: RTCVideoTrack?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        print("[TestVideoController] 🔴 viewDidLoad")
        setupUI()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        print("[TestVideoController] 🔴 viewWillAppear")
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        print("[TestVideoController] 🔴 viewDidAppear - SUCCESS!")
        print("[TestVideoController] 🔴 View frame: \(view.frame)")
        print("[TestVideoController] 🔴 View bounds: \(view.bounds)")
        print("[TestVideoController] 🔴 View window: \(view.window != nil ? "EXISTS" : "NIL")")
        
        // 延迟检查
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.performDetailedCheck()
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        print("[TestVideoController] 🔴 viewDidLayoutSubviews")
        print("[TestVideoController] 🔴 View frame: \(view.frame)")
        print("[TestVideoController] 🔴 View bounds: \(view.bounds)")
    }
    
    private func setupUI() {
        print("[TestVideoController] 🔴 Setting up UI")
        
        // 设置明显的背景色
        view.backgroundColor = UIColor.systemGreen
        
        // 添加标识标签
        let label = UILabel()
        label.text = "🟢 TEST VIDEO CONTROLLER 🟢"
        label.textColor = .white
        label.font = UIFont.boldSystemFont(ofSize: 18)
        label.textAlignment = .center
        label.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        label.layer.cornerRadius = 8
        label.clipsToBounds = true
        label.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(label)
        
        // 创建视频视图
        createVideoView()
        
        // 设置约束
        NSLayoutConstraint.activate([
            label.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            label.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            label.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            label.heightAnchor.constraint(equalToConstant: 40)
        ])
        
        print("[TestVideoController] 🔴 UI setup complete")
    }
    
    private func createVideoView() {
        if self.videoView != nil {
            print("[TestVideoController] 🔴 Video view already exists, skipping creation")
            return
        }

        let videoView = RTCMTLVideoView()
        videoView.backgroundColor = UIColor.systemYellow
        videoView.contentMode = .scaleAspectFit
        videoView.translatesAutoresizingMaskIntoConstraints = false

        // 添加明显的边框
        videoView.layer.borderColor = UIColor.systemPink.cgColor
        videoView.layer.borderWidth = 3.0
        videoView.layer.cornerRadius = 8

        view.addSubview(videoView)
        self.videoView = videoView

        // 设置约束
        NSLayoutConstraint.activate([
            videoView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            videoView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            videoView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 80),
            videoView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20)
        ])

        print("[TestVideoController] 🔴 Video view created and added")

        // 强制立即布局
        DispatchQueue.main.async {
            self.view.setNeedsLayout()
            self.view.layoutIfNeeded()
            print("[TestVideoController] 🔴 Forced layout update")
            print("[TestVideoController] 🔴 Video view frame after layout: \(videoView.frame)")
            print("[TestVideoController] 🔴 Video view bounds after layout: \(videoView.bounds)")
        }
    }
    
    func updateVideoTrack(_ videoTrack: RTCVideoTrack?) {
        print("[TestVideoController] 🔴 updateVideoTrack called")
        print("[TestVideoController] 🔴 New video track: \(videoTrack != nil ? "EXISTS" : "NIL")")
        
        // 移除旧的视频轨道
        if let oldTrack = currentVideoTrack, let videoView = self.videoView {
            oldTrack.remove(videoView)
            print("[TestVideoController] 🔴 Removed old video track")
        }
        
        // 添加新的视频轨道
        if let newTrack = videoTrack, let videoView = self.videoView {
            newTrack.add(videoView)
            currentVideoTrack = newTrack
            print("[TestVideoController] 🔴 Added new video track")
            
            // 强制布局更新
            DispatchQueue.main.async {
                self.view.setNeedsLayout()
                self.view.layoutIfNeeded()
                print("[TestVideoController] 🔴 Forced layout update")
            }
        } else {
            currentVideoTrack = nil
            print("[TestVideoController] 🔴 No video track to add")
        }
    }
    
    private func performDetailedCheck() {
        print("[TestVideoController] 🔴 🔍 Performing detailed check:")
        print("[TestVideoController] 🔴 - Main view frame: \(view.frame)")
        print("[TestVideoController] 🔴 - Main view bounds: \(view.bounds)")
        print("[TestVideoController] 🔴 - Main view window: \(view.window != nil ? "EXISTS" : "NIL")")
        print("[TestVideoController] 🔴 - Main view backgroundColor: \(view.backgroundColor?.description ?? "NIL")")
        print("[TestVideoController] 🔴 - Main view isHidden: \(view.isHidden)")
        print("[TestVideoController] 🔴 - Main view alpha: \(view.alpha)")
        
        if let videoView = self.videoView {
            print("[TestVideoController] 🔴 - Video view frame: \(videoView.frame)")
            print("[TestVideoController] 🔴 - Video view bounds: \(videoView.bounds)")
            print("[TestVideoController] 🔴 - Video view window: \(videoView.window != nil ? "EXISTS" : "NIL")")
            print("[TestVideoController] 🔴 - Video view superview: \(videoView.superview != nil ? "EXISTS" : "NIL")")
            print("[TestVideoController] 🔴 - Video view backgroundColor: \(videoView.backgroundColor?.description ?? "NIL")")
            print("[TestVideoController] 🔴 - Video view isHidden: \(videoView.isHidden)")
            print("[TestVideoController] 🔴 - Video view alpha: \(videoView.alpha)")
            print("[TestVideoController] 🔴 - Current video track: \(currentVideoTrack != nil ? "EXISTS" : "NIL")")
        }
    }
}

// MARK: - Preview

struct TestVideoView_Previews: PreviewProvider {
    static var previews: some View {
        TestVideoView(webRTCStreamerClient: WebRTCStreamerClient())
    }
}
