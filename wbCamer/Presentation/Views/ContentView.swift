//
//  ContentView.swift
//  wbCamer
//
//  Created by REKROW on 2025/6/25.
//

import SwiftUI

struct ContentView: View {
    @ObservedObject private var serviceDiscovery = EagleServiceDiscovery.shared
    @StateObject private var networkPermissionManager = NetworkPermissionManager()
    @StateObject private var appConfig = AppConfiguration.shared

    @State private var showingPermissionAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 网络权限状态 - 只在完成初始检查且权限获取失败时显示
                if networkPermissionManager.hasCompletedInitialCheck && !networkPermissionManager.hasLocalNetworkPermission {
                    VStack {
                        Image(systemName: "wifi.exclamationmark")
                            .font(.system(size: 30))
                            .foregroundColor(.orange)
                        Text("需要本地网络访问权限")
                            .font(.headline)
                        Text("请在设置中允许访问本地网络以发现ZCam设备")
                            .font(.caption)
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                        Button("检查权限") {
                            networkPermissionManager.requestPermissionIfNeeded()
                        }
                        .padding(.top, 5)
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(10)
                }
                
                // 标题和状态
                VStack(spacing: 10) {
                    HStack {
                        Image(systemName: "camera.fill")
                            .foregroundColor(.blue)
                            .font(.title2)
                        Text("ZCam Camera Discovery")
                            .font(.title2)
                            .fontWeight(.semibold)
                    }
                    
                    if serviceDiscovery.isScanning {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("正在扫描局域网...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    if let lastScanTime = serviceDiscovery.lastScanTime {
                        Text("上次扫描: \(formatTime(lastScanTime))")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.top)
                
                // 控制按钮
                VStack(spacing: 15) {
                    HStack(spacing: 20) {
                        Button(action: {
                            // 先检查权限，再开始扫描
                            if !networkPermissionManager.hasLocalNetworkPermission {
                                networkPermissionManager.checkLocalNetworkPermission()
                                // 给权限检查一些时间，然后再尝试扫描
                                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                    if networkPermissionManager.hasLocalNetworkPermission {
                                        serviceDiscovery.startManualScan()
                                    }
                                }
                            } else {
                                serviceDiscovery.startManualScan()
                            }
                        }) {
                            HStack {
                                Image(systemName: "magnifyingglass")
                                Text("手动扫描")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(networkPermissionManager.hasLocalNetworkPermission ? Color.blue : Color.gray)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .disabled(serviceDiscovery.isScanning)
                        
                        Button(action: {
                            serviceDiscovery.clearResults()
                        }) {
                            HStack {
                                Image(systemName: "trash")
                                Text("清除结果")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.red)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                    }
                }
                
                // 发现的IP地址列表
                VStack(alignment: .leading, spacing: 10) {
                    HStack {
                        Text("发现的设备IP (\(serviceDiscovery.discoveredIPs.count))")
                            .font(.headline)
                        Spacer()
                    }
                    
                    if serviceDiscovery.discoveredIPs.isEmpty {
                        VStack {
                            Image(systemName: "wifi.slash")
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                            Text("未发现设备")
                                .font(.body)
                                .foregroundColor(.secondary)
                            Text("请确保设备在同一局域网内")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 30)
                    } else {
                        ScrollView {
                            LazyVStack(spacing: 8) {
                                ForEach(serviceDiscovery.discoveredIPs, id: \.self) { ip in
                                    IPAddressRow(ipAddress: ip)
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                    }
                }
                .padding(.horizontal)
                
                // 详细服务信息已隐藏
                
                Spacer()
            }
        .navigationTitle("wbCamer")
        .navigationBarTitleDisplayMode(.inline)
        .alert("需要网络权限", isPresented: $showingPermissionAlert) {
            Button("设置") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("请在设置中允许此应用访问本地网络以发现ZCam设备")
        }
        .onAppear {
            // 延迟执行权限检查，避免启动时立即显示权限提示
            networkPermissionManager.performDelayedInitialCheck()
            // 注意：自动扫描已禁用，初始扫描会在EagleServiceDiscovery初始化时自动执行
        }
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        return formatter.string(from: date)
    }
}

// MARK: - IP地址行组件
struct IPAddressRow: View {
    let ipAddress: String
    @State private var copied = false
    @StateObject private var cameraManager = CameraManager.shared

    var body: some View {
        Button(action: {
            print("👆 User clicked on IP in ContentView: \(ipAddress)")

            // 从 IP 字符串中提取纯 IP 地址（去除端口信息）
            let cleanIP = ipAddress.components(separatedBy: ":").first ?? ipAddress
            print("📱 Creating CameraDevice for IP: \(cleanIP) - HTTP API: port 80, WebRTC: port 8000")

            let camera = CameraDevice(
                name: "Zcam Camera",
                ipAddress: cleanIP,
                port: 80  // P2-R1 HTTP API 使用端口 80，WebRTC Streamer 使用端口 8000
            )

            print("🚀 Calling cameraManager.connect() for \(cleanIP):80 (HTTP API)")
            cameraManager.connect(to: camera)
        }) {
            HStack {
                Image(systemName: "network")
                    .foregroundColor(.green)

                Text(ipAddress)
                    .font(.monospaced(.body)())
                    .foregroundColor(.primary)

                Spacer()

                HStack(spacing: 8) {
                    // 连接按钮
                    Button(action: {
                        print("👆 User clicked connect button for IP: \(ipAddress)")

                        // 从 IP 字符串中提取纯 IP 地址（去除端口信息）
                        let cleanIP = ipAddress.components(separatedBy: ":").first ?? ipAddress
                        print("📱 Creating CameraDevice for IP: \(cleanIP) - HTTP API: port 80, WebRTC: port 8000")

                        let camera = CameraDevice(
                            name: "ZCam Camera",
                            ipAddress: cleanIP,
                            port: 80  // P2-R1 HTTP API 使用端口 80，WebRTC Streamer 使用端口 8000
                        )

                        print("🚀 Calling cameraManager.connect() for \(cleanIP):80 (HTTP API)")
                        cameraManager.connect(to: camera)
                    }) {
                        Image(systemName: "play.circle")
                            .foregroundColor(.blue)
                    }

                    // 复制按钮
                    Button(action: {
                        UIPasteboard.general.string = ipAddress
                        copied = true
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            copied = false
                        }
                    }) {
                        Image(systemName: copied ? "checkmark" : "doc.on.doc")
                            .foregroundColor(copied ? .green : .blue)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .onAppear {
                print("🔘 IPAddressRow created for IP: \(ipAddress)")
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 服务详情行组件
struct ServiceDetailRow: View {
    let service: EagleServiceInstance
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(service.instanceName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Spacer()
                if service.isResolved {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                }
            }
            
            if let ip = service.ipAddress {
                Text("IP: \(ip)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            if let port = service.port {
                Text("Port: \(port)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    ContentView()
}
