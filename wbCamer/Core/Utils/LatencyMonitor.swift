//
//  LatencyMonitor.swift
//  wbCamer
//
//  Created by AI Assistant on 2025/7/1.
//

import Foundation
import Combine

/// 延迟监控工具
/// 用于测量PTZ控制和视频流的延迟性能
class LatencyMonitor: ObservableObject {
    static let shared = LatencyMonitor()
    
    // MARK: - Published Properties
    @Published var averagePTZLatency: Double = 0.0  // ms
    @Published var averageVideoLatency: Double = 0.0  // ms
    @Published var ptzLatencyHistory: [Double] = []
    @Published var videoLatencyHistory: [Double] = []
    
    // MARK: - Private Properties
    private var ptzLatencyMeasurements: [Double] = []
    private var videoLatencyMeasurements: [Double] = []
    private let maxHistoryCount = 100  // 保留最近100次测量
    
    private init() {}
    
    // MARK: - PTZ Latency Monitoring
    
    /// 开始PTZ延迟测量
    /// - Returns: 测量ID，用于结束测量
    func startPTZLatencyMeasurement() -> String {
        let measurementId = UUID().uuidString
        let startTime = Date()
        
        // 存储开始时间
        ptzMeasurementStarts[measurementId] = startTime
        
        return measurementId
    }
    
    /// 结束PTZ延迟测量
    /// - Parameter measurementId: 测量ID
    func endPTZLatencyMeasurement(_ measurementId: String) {
        guard let startTime = ptzMeasurementStarts[measurementId] else {
            print("[LatencyMonitor] ⚠️ PTZ measurement ID not found: \(measurementId)")
            return
        }
        
        let endTime = Date()
        let latency = endTime.timeIntervalSince(startTime) * 1000  // 转换为毫秒
        
        // 记录延迟
        recordPTZLatency(latency)
        
        // 清理
        ptzMeasurementStarts.removeValue(forKey: measurementId)
        
        print("[LatencyMonitor] 📊 PTZ Latency: \(String(format: "%.1f", latency))ms")
    }
    
    /// 直接测量PTZ延迟（简化版本）
    /// - Parameters:
    ///   - start: 开始时间
    ///   - end: 结束时间
    static func measurePTZLatency(start: Date, end: Date) {
        let latency = end.timeIntervalSince(start) * 1000  // ms
        shared.recordPTZLatency(latency)
        print("[LatencyMonitor] 📊 PTZ Latency: \(String(format: "%.1f", latency))ms")
    }
    
    // MARK: - Video Latency Monitoring
    
    /// 测量视频帧延迟
    /// - Parameter frameTimestamp: 帧时间戳
    static func measureVideoLatency(frameTimestamp: Date) {
        let latency = Date().timeIntervalSince(frameTimestamp) * 1000
        shared.recordVideoLatency(latency)
        
        // 只在延迟较高时打印，避免日志过多
        if latency > 100 {
            print("[LatencyMonitor] 📺 Video Latency: \(String(format: "%.1f", latency))ms")
        }
    }
    
    /// 测量WebRTC连接延迟
    /// - Parameter connectionStart: 连接开始时间
    static func measureWebRTCConnectionLatency(connectionStart: Date) {
        let latency = Date().timeIntervalSince(connectionStart) * 1000
        print("[LatencyMonitor] 🔗 WebRTC Connection Latency: \(String(format: "%.1f", latency))ms")
    }
    
    // MARK: - Private Methods
    
    private var ptzMeasurementStarts: [String: Date] = [:]
    
    private func recordPTZLatency(_ latency: Double) {
        DispatchQueue.main.async {
            self.ptzLatencyMeasurements.append(latency)
            self.ptzLatencyHistory.append(latency)
            
            // 限制历史记录数量
            if self.ptzLatencyHistory.count > self.maxHistoryCount {
                self.ptzLatencyHistory.removeFirst()
            }
            
            // 计算平均延迟（最近20次测量）
            let recentMeasurements = Array(self.ptzLatencyMeasurements.suffix(20))
            self.averagePTZLatency = recentMeasurements.reduce(0, +) / Double(recentMeasurements.count)
        }
    }
    
    private func recordVideoLatency(_ latency: Double) {
        DispatchQueue.main.async {
            self.videoLatencyMeasurements.append(latency)
            self.videoLatencyHistory.append(latency)
            
            // 限制历史记录数量
            if self.videoLatencyHistory.count > self.maxHistoryCount {
                self.videoLatencyHistory.removeFirst()
            }
            
            // 计算平均延迟（最近50次测量）
            let recentMeasurements = Array(self.videoLatencyMeasurements.suffix(50))
            self.averageVideoLatency = recentMeasurements.reduce(0, +) / Double(recentMeasurements.count)
        }
    }
    
    // MARK: - Statistics
    
    /// 获取PTZ延迟统计信息
    func getPTZLatencyStats() -> LatencyStats {
        let measurements = Array(ptzLatencyMeasurements.suffix(50))
        return calculateStats(from: measurements)
    }
    
    /// 获取视频延迟统计信息
    func getVideoLatencyStats() -> LatencyStats {
        let measurements = Array(videoLatencyMeasurements.suffix(100))
        return calculateStats(from: measurements)
    }
    
    private func calculateStats(from measurements: [Double]) -> LatencyStats {
        guard !measurements.isEmpty else {
            return LatencyStats(average: 0, min: 0, max: 0, p95: 0, count: 0)
        }

        let sorted = measurements.sorted()
        let average = measurements.reduce(0, +) / Double(measurements.count)
        let minValue = sorted.first ?? 0
        let maxValue = sorted.last ?? 0
        let p95Index = Int(Double(sorted.count) * 0.95)
        let p95 = sorted[Swift.min(p95Index, sorted.count - 1)]

        return LatencyStats(
            average: average,
            min: minValue,
            max: maxValue,
            p95: p95,
            count: measurements.count
        )
    }
    
    /// 重置所有测量数据
    func reset() {
        DispatchQueue.main.async {
            self.ptzLatencyMeasurements.removeAll()
            self.videoLatencyMeasurements.removeAll()
            self.ptzLatencyHistory.removeAll()
            self.videoLatencyHistory.removeAll()
            self.averagePTZLatency = 0.0
            self.averageVideoLatency = 0.0
        }
    }
    
    /// 打印延迟报告
    func printLatencyReport() {
        let ptzStats = getPTZLatencyStats()
        let videoStats = getVideoLatencyStats()
        
        print("""
        
        📊 ===== 延迟性能报告 =====
        
        🎮 PTZ控制延迟:
           平均: \(String(format: "%.1f", ptzStats.average))ms
           最小: \(String(format: "%.1f", ptzStats.min))ms
           最大: \(String(format: "%.1f", ptzStats.max))ms
           P95:  \(String(format: "%.1f", ptzStats.p95))ms
           样本: \(ptzStats.count)次
        
        📺 视频流延迟:
           平均: \(String(format: "%.1f", videoStats.average))ms
           最小: \(String(format: "%.1f", videoStats.min))ms
           最大: \(String(format: "%.1f", videoStats.max))ms
           P95:  \(String(format: "%.1f", videoStats.p95))ms
           样本: \(videoStats.count)次
        
        ========================
        
        """)
    }
}

// MARK: - Latency Statistics

struct LatencyStats {
    let average: Double  // 平均延迟 (ms)
    let min: Double      // 最小延迟 (ms)
    let max: Double      // 最大延迟 (ms)
    let p95: Double      // 95百分位延迟 (ms)
    let count: Int       // 样本数量
}

// MARK: - Convenience Extensions

extension LatencyMonitor {
    
    /// 便捷方法：测量代码块执行延迟
    /// - Parameters:
    ///   - label: 标签
    ///   - block: 要测量的代码块
    static func measure<T>(label: String, block: () throws -> T) rethrows -> T {
        let start = Date()
        let result = try block()
        let end = Date()
        let latency = end.timeIntervalSince(start) * 1000
        print("[LatencyMonitor] ⏱️ \(label): \(String(format: "%.1f", latency))ms")
        return result
    }
    
    /// 便捷方法：测量异步代码块延迟
    /// - Parameters:
    ///   - label: 标签
    ///   - block: 要测量的异步代码块
    static func measureAsync<T>(label: String, block: () async throws -> T) async rethrows -> T {
        let start = Date()
        let result = try await block()
        let end = Date()
        let latency = end.timeIntervalSince(start) * 1000
        print("[LatencyMonitor] ⏱️ \(label): \(String(format: "%.1f", latency))ms")
        return result
    }
}
