//
//  RTSPVideoPlayer.swift
//  wbCamer
//
//  Created by AI Assistant on 2025/7/1.
//

import Foundation
import AVFoundation
import Combine
import UIKit

/// RTSP视频播放器
/// 直接连接RTSP流，绕过WebRTC Streamer以降低延迟
@MainActor
class RTSPVideoPlayer: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var isConnected: Bool = false
    @Published var isConnecting: Bool = false
    @Published var connectionError: Error?
    @Published var playerLayer: AVPlayerLayer?
    
    // MARK: - Private Properties
    private var player: AVPlayer?
    private var playerItem: AVPlayerItem?
    private var timeObserver: Any?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - RTSP Configuration
    private var currentRTSPURL: String?
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupAudioSession()
    }
    
    deinit {
        // 在deinit中不能使用async调用，直接清理
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
        }
        player?.pause()
        player?.replaceCurrentItem(with: nil)
    }
    
    // MARK: - Public Methods
    
    /// 连接到RTSP流
    /// - Parameters:
    ///   - cameraIP: 相机IP地址
    ///   - port: RTSP端口（默认554）
    func connect(to cameraIP: String, port: Int = 554) async {
        print("[RTSPPlayer] 🎥 Connecting to RTSP stream at \(cameraIP):\(port)")
        
        guard !isConnecting else {
            print("[RTSPPlayer] ⚠️ Connection already in progress")
            return
        }
        
        isConnecting = true
        connectionError = nil
        
        // 尝试多个可能的RTSP URL
        let possibleURLs = generateRTSPURLs(cameraIP: cameraIP, port: port)
        
        for rtspURL in possibleURLs {
            print("[RTSPPlayer] 🔗 Trying RTSP URL: \(rtspURL)")
            
            if await attemptConnection(to: rtspURL) {
                print("[RTSPPlayer] ✅ Successfully connected to: \(rtspURL)")
                currentRTSPURL = rtspURL
                isConnected = true
                isConnecting = false
                return
            }
        }
        
        // 所有URL都失败
        let error = RTSPError.connectionFailed("Failed to connect to any RTSP URL")
        print("[RTSPPlayer] ❌ All RTSP URLs failed: \(error)")
        connectionError = error
        isConnecting = false
    }
    
    /// 断开RTSP连接
    func disconnect() {
        print("[RTSPPlayer] 🔌 Disconnecting RTSP stream")
        
        // 移除时间观察者
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }
        
        // 停止播放器
        player?.pause()
        player?.replaceCurrentItem(with: nil)
        player = nil
        playerItem = nil
        playerLayer = nil
        
        isConnected = false
        isConnecting = false
        currentRTSPURL = nil
        connectionError = nil
        
        print("[RTSPPlayer] ✅ RTSP stream disconnected")
    }
    
    /// 获取播放器层用于视频显示
    func getPlayerLayer() -> AVPlayerLayer? {
        return playerLayer
    }
    
    // MARK: - Private Methods
    
    private func generateRTSPURLs(cameraIP: String, port: Int) -> [String] {
        // 基于P2-R1官方文档和常见RTSP配置生成可能的URL
        // 官方文档: https://github.com/imaginevision/Z-Camera-Doc/blob/master/E2/protocol/http/http.md
        return [
            // P2-R1官方RTSP URL（最高优先级）
            "rtsp://\(cameraIP)/live_stream",
            "rtsp://\(cameraIP):554/live_stream",

            // P2-R1备用RTSP URL（基于之前的分析）
            "rtsp://\(cameraIP)/live_h264",
            "rtsp://\(cameraIP):554/live_h264",

            // 常见的RTSP路径
            "rtsp://\(cameraIP)/stream",
            "rtsp://\(cameraIP):554/stream",
            "rtsp://\(cameraIP)/live",
            "rtsp://\(cameraIP):554/live",
            "rtsp://\(cameraIP)/video",
            "rtsp://\(cameraIP):554/video",

            // 带认证的RTSP URL（如果需要）
            "rtsp://admin:admin@\(cameraIP)/live_stream",
            "rtsp://admin:admin@\(cameraIP):554/live_stream",
            "rtsp://admin:admin@\(cameraIP)/live_h264",
            "rtsp://admin:admin@\(cameraIP):554/live_h264",

            // 自定义端口
            "rtsp://\(cameraIP):\(port)/live_stream",
            "rtsp://\(cameraIP):\(port)/live_h264",
            "rtsp://\(cameraIP):\(port)/stream",
            "rtsp://\(cameraIP):\(port)/live",

            // 备用路径
            "rtsp://\(cameraIP)/cam/realmonitor?channel=1&subtype=0",
            "rtsp://\(cameraIP)/h264",
            "rtsp://\(cameraIP)/mpeg4"
        ]
    }
    
    private func attemptConnection(to rtspURL: String) async -> Bool {
        return await withCheckedContinuation { continuation in
            guard let url = URL(string: rtspURL) else {
                print("[RTSPPlayer] ❌ Invalid RTSP URL: \(rtspURL)")
                continuation.resume(returning: false)
                return
            }
            
            // 创建AVPlayerItem
            let asset = AVURLAsset(url: url)
            let playerItem = AVPlayerItem(asset: asset)
            
            // 监听播放器状态
            var statusObserver: NSKeyValueObservation?
            var errorObserver: NSKeyValueObservation?
            
            let cleanup = {
                statusObserver?.invalidate()
                errorObserver?.invalidate()
            }
            
            // 监听播放器项目状态
            statusObserver = playerItem.observe(\.status, options: [.new]) { [weak self] item, _ in
                DispatchQueue.main.async {
                    switch item.status {
                    case .readyToPlay:
                        print("[RTSPPlayer] ✅ Player item ready to play")
                        self?.setupPlayer(with: playerItem)
                        cleanup()
                        continuation.resume(returning: true)
                        
                    case .failed:
                        if let error = item.error {
                            print("[RTSPPlayer] ❌ Player item failed: \(error)")
                        }
                        cleanup()
                        continuation.resume(returning: false)
                        
                    case .unknown:
                        print("[RTSPPlayer] ⏳ Player item status unknown")
                        
                    @unknown default:
                        print("[RTSPPlayer] ⚠️ Unknown player item status")
                        cleanup()
                        continuation.resume(returning: false)
                    }
                }
            }
            
            // 监听错误
            errorObserver = playerItem.observe(\.error, options: [.new]) { item, _ in
                if let error = item.error {
                    print("[RTSPPlayer] ❌ Player item error: \(error)")
                    DispatchQueue.main.async {
                        cleanup()
                        continuation.resume(returning: false)
                    }
                }
            }
            
            // 设置超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
                if playerItem.status == .unknown {
                    print("[RTSPPlayer] ⏰ Connection timeout for: \(rtspURL)")
                    cleanup()
                    continuation.resume(returning: false)
                }
            }
            
            // 开始加载
            self.playerItem = playerItem
        }
    }
    
    private func setupPlayer(with playerItem: AVPlayerItem) {
        // 创建播放器
        player = AVPlayer(playerItem: playerItem)
        
        // 创建播放器层
        let layer = AVPlayerLayer(player: player)
        layer.videoGravity = .resizeAspect
        layer.backgroundColor = UIColor.black.cgColor
        playerLayer = layer
        
        // 开始播放
        player?.play()
        
        // 添加时间观察者用于延迟监控
        let interval = CMTime(seconds: 1.0, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        timeObserver = player?.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            // 可以在这里添加延迟监控逻辑
            Task { @MainActor in
                self?.monitorPlaybackLatency(currentTime: time)
            }
        }
        
        print("[RTSPPlayer] 🎬 Player setup completed, starting playback")
    }
    
    private func monitorPlaybackLatency(currentTime: CMTime) {
        // 延迟监控：测量RTSP流的播放延迟
        if let playerItem = playerItem {
            let currentTime = playerItem.currentTime()

            // 计算缓冲延迟
            if let timebase = playerItem.timebase {
                let hostTime = CMClockGetTime(CMClockGetHostTimeClock())
                let currentHostTime = CMSyncConvertTime(hostTime, from: CMClockGetHostTimeClock(), to: timebase)
                let latency = CMTimeGetSeconds(CMTimeSubtract(currentHostTime, currentTime)) * 1000 // ms

                // 只在延迟较高时记录
                if latency > 100 {
                    LatencyMonitor.measureVideoLatency(frameTimestamp: Date().addingTimeInterval(-latency/1000))
                }
            }
        }
    }
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .videoRecording, options: [.allowBluetooth])
            try audioSession.setActive(true)
            print("[RTSPPlayer] 🔊 Audio session configured")
        } catch {
            print("[RTSPPlayer] ⚠️ Failed to setup audio session: \(error)")
        }
    }
}

// MARK: - RTSP Error Types

enum RTSPError: LocalizedError {
    case invalidURL(String)
    case connectionFailed(String)
    case streamNotAvailable(String)
    case authenticationRequired
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL(let url):
            return "Invalid RTSP URL: \(url)"
        case .connectionFailed(let message):
            return "RTSP connection failed: \(message)"
        case .streamNotAvailable(let message):
            return "RTSP stream not available: \(message)"
        case .authenticationRequired:
            return "RTSP authentication required"
        case .networkError(let error):
            return "RTSP network error: \(error.localizedDescription)"
        }
    }
}
