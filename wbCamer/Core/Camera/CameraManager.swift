//
//  CameraManager.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Combine
import Network
import AVFoundation

// MARK: - Camera Manager

class CameraManager: ObservableObject {
    static let shared = CameraManager()
    
    // MARK: - Published Properties
    @Published var isConnected = false  // 整体连接状态，基于WebRTC和基本HTTP连接
    @Published var isConnecting = false
    @Published var currentCamera: CameraDevice?
    @Published var cameraStatus: CameraStatus?
    @Published var isRecording = false
    @Published var recordingDuration: TimeInterval = 0
    @Published var batteryLevel: Int = 0
    @Published var storageInfo: StorageInfo?
    @Published var connectionError: Error?

    // 新增：是否应该显示视频预览界面（基于WebRTC连接状态）
    @Published var shouldShowVideoInterface = false

    // Work mode tracking
    private var workModeLastUpdated: Date?
    @Published var availableCameras: [CameraDevice] = []
    
    // MARK: - Private Properties
    private let apiClient = APIClient.shared
    private let webSocketManager = WebSocketManager.shared
    private let networkMonitor = NetworkMonitor.shared
    private let coreDataStack = CoreDataStack.shared
    
    // WebRTC support (使用 WebRTC Streamer 客户端)
    @Published var webRTCStreamerClient: WebRTCStreamerClient?
    @Published var isWebRTCConnected = false
    
    private var cancellables = Set<AnyCancellable>()
    private var statusUpdateTimer: Timer?
    private var recordingTimer: Timer?
    private var discoveryTimer: Timer?
    
    // MARK: - Initialization
    
    private init() {
        print("🏗️ CameraManager initialized")
        setupBindings()
        _ = loadSavedCameras()
        print("✅ CameraManager setup complete")
    }
    
    // MARK: - Public Methods
    
    func discoverCameras() {
        guard case .reachable = networkMonitor.networkInfo.status else {
            print("Network not available for camera discovery")
            return
        }
        
        // Start network scanning for cameras
        scanForCamerasOnNetwork { [weak self] cameras in
            DispatchQueue.main.async {
                self?.availableCameras = cameras
                self?.saveCamerasToStorage(cameras)
            }
        }
    }
    
    func connect(to camera: CameraDevice) {
        print("📞 CameraManager.connect() called for \(camera.ipAddress):\(camera.port)")

        guard !isConnecting else {
            print("⚠️ Connection already in progress, ignoring new connection request")
            return
        }

        print("🔗 Attempting to connect to camera: \(camera.name) at \(camera.ipAddress):\(camera.port)")

        isConnecting = true
        connectionError = nil
        currentCamera = camera

        // Update APIClient base URL to match the camera's IP
        // Don't include port 80 in URL as it's the default HTTP port
        let baseURL: String
        if camera.port == 80 {
            baseURL = "http://\(camera.ipAddress)"
        } else {
            baseURL = "http://\(camera.ipAddress):\(camera.port)"
        }
        apiClient.updateBaseURL(to: baseURL)

        // Configure PTZManager with the APIClient
        Task { @MainActor in
            PTZManager.shared.configure(with: apiClient)
            print("✅ PTZManager configured with APIClient for \(camera.ipAddress)")
        }

        // Test camera connection
        networkMonitor.testCameraConnectivity(to: camera.ipAddress, port: Int(camera.port)) { [weak self] success, latency in
            DispatchQueue.main.async {
                if success {
                    print("✅ Camera connectivity test passed (latency: \(latency?.description ?? "unknown"))")
                    self?.establishCameraConnection(to: camera)
                } else {
                    print("❌ Camera connectivity test failed")
                    self?.handleConnectionFailure(APIError.networkError(NSError(domain: "CameraManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "Connection failed"])))
                }
            }
        }
    }
    
    func disconnect() {
        stopStatusUpdates()
        stopRecordingTimer()
        webSocketManager.disconnect()
        forceDisconnectWebRTC()  // 使用强制断开，因为这是真正的相机断开

        isConnected = false
        isConnecting = false
        currentCamera = nil
        cameraStatus = nil
        connectionError = nil

        // 更新视频界面可见性
        updateVideoInterfaceVisibility()
    }
    
    func reconnect() {
        guard let camera = currentCamera else { return }
        disconnect()
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.connect(to: camera)
        }
    }
    
    // MARK: - Camera Control Methods
    
    func startRecording() {
        guard isConnected, let _ = currentCamera else { return }

        let request = RecordingControlAPIRequest(action: .start)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to start recording: \(error)")
                    }
                },
                receiveValue: { [weak self] (response: BasicResponse) in
                    if response.code == 0 {
                        self?.isRecording = true
                        self?.startRecordingTimer()
                    } else {
                        print("Recording start failed: \(response.desc ?? "Unknown error")")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func stopRecording() {
        guard isConnected, let _ = currentCamera else { return }

        let request = RecordingControlAPIRequest(action: .stop)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to stop recording: \(error)")
                    }
                },
                receiveValue: { [weak self] (response: BasicResponse) in
                    if response.code == 0 {
                        self?.isRecording = false
                        self?.stopRecordingTimer()
                    } else {
                        print("Recording stop failed: \(response.desc ?? "Unknown error")")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func updateCameraSettings(_ settings: CameraSettings) {
        guard isConnected, let _ = currentCamera else { return }
        
        // 使用官方API逐个设置参数
        if let iso = settings.iso {
            let isoRequest = SetCameraSettingsRequest(key: "iso", value: String(iso))
            apiClient.request(isoRequest)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            print("Failed to set ISO: \(error)")
                        }
                    },
                    receiveValue: { (response: BasicResponse) in
                        if response.code == 0 {
                            print("ISO set successfully")
                        }
                    }
                )
                .store(in: &cancellables)
        }

        // 设置白平衡
        let wbRequest = SetCameraSettingsRequest(key: "wb", value: settings.whiteBalance.rawValue)
        apiClient.request(wbRequest)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to set white balance: \(error)")
                    }
                },
                receiveValue: { (response: BasicResponse) in
                    if response.code == 0 {
                        print("White balance set successfully")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func capturePhoto() {
        guard isConnected, let _ = currentCamera else { return }

        let request = CapturePhotoRequest()

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to capture photo: \(error)")
                        print("⚠️ Note: /ctrl/snapshot API may not be supported by this device")
                    }
                },
                receiveValue: { (response: BasicResponse) in
                    if response.code == 0 {
                        print("Photo captured successfully")
                    } else {
                        print("Photo capture failed: \(response.desc ?? "Unknown error")")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - WebRTC Methods
    
    @MainActor
    func initializeWebRTC(for camera: CameraDevice) {
        guard webRTCStreamerClient == nil else {
            print("[CameraManager] WebRTC client already exists, skipping initialization")
            return
        }

        print("[CameraManager] Initializing WebRTC client...")
        webRTCStreamerClient = WebRTCStreamerClient()
        print("[CameraManager] WebRTC client created: \(webRTCStreamerClient != nil)")

        // Monitor WebRTC connection state
        webRTCStreamerClient?.$connectionState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                print("[CameraManager] WebRTC connection state changed: \(state)")
                let isWebRTCConnected = (state == .connected)
                self?.isWebRTCConnected = isWebRTCConnected

                // 更新是否应该显示视频界面
                self?.updateVideoInterfaceVisibility()
            }
            .store(in: &cancellables)
    }
    
    func connectWebRTC(to camera: CameraDevice) {
        Task { @MainActor in
            if webRTCStreamerClient == nil {
                initializeWebRTC(for: camera)
            }

            guard let webRTCStreamerClient = webRTCStreamerClient else {
                print("❌ WebRTC client not available")
                return
            }

            do {
                // P2-R1 WebRTC Streamer 运行在端口 8000，而不是 HTTP API 的端口 80
                let webrtcPort = 8000
                print("🎥 Connecting to WebRTC Streamer at \(camera.ipAddress):\(webrtcPort)")
                try await webRTCStreamerClient.connect(to: camera.ipAddress, port: webrtcPort)
                print("✅ WebRTC Streamer connected successfully to \(camera.name)")
                self.isWebRTCConnected = true

                // 更新视频界面可见性 - 修复Sendable警告
                await MainActor.run {
                    self.updateVideoInterfaceVisibility()
                }
            } catch {
                print("❌ Failed to connect WebRTC Streamer: \(error)")
                self.isWebRTCConnected = false

                // 更新视频界面可见性 - 修复Sendable警告
                await MainActor.run {
                    self.updateVideoInterfaceVisibility()
                    self.connectionError = error
                }

                // 尝试重新连接（最多3次）
                await retryWebRTCConnection(to: camera, attempt: 1)
            }
        }
    }

    private func retryWebRTCConnection(to camera: CameraDevice, attempt: Int) async {
        guard attempt <= 3 else {
            print("❌ WebRTC connection failed after 3 attempts")
            return
        }

        print("🔄 Retrying WebRTC connection (attempt \(attempt)/3)...")

        // 等待一段时间后重试
        try? await Task.sleep(nanoseconds: UInt64(attempt * 2_000_000_000)) // 2秒 * 尝试次数

        guard let webRTCStreamerClient = webRTCStreamerClient else { return }

        do {
            let webrtcPort = 8000
            try await webRTCStreamerClient.connect(to: camera.ipAddress, port: webrtcPort)
            print("✅ WebRTC Streamer connected successfully on retry \(attempt)")
            self.isWebRTCConnected = true
        } catch {
            print("❌ WebRTC retry \(attempt) failed: \(error)")
            await retryWebRTCConnection(to: camera, attempt: attempt + 1)
        }
    }
    
    func disconnectWebRTC() {
        Task { @MainActor in
            print("[CameraManager] Disconnecting WebRTC...")

            // 先断开连接，但保持对象引用
            webRTCStreamerClient?.disconnect()
            isWebRTCConnected = false

            // 延迟清理对象引用，确保断开操作完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                print("[CameraManager] Cleaning up WebRTC client reference")
                self?.webRTCStreamerClient = nil
            }
        }
    }

    // 新增：仅在真正需要时断开WebRTC（如相机断开连接）
    func forceDisconnectWebRTC() {
        disconnectWebRTC()
    }

    // 检查并恢复WebRTC连接状态
    func ensureWebRTCConnection(for camera: CameraDevice) {
        guard isConnected else { return }

        Task { @MainActor in
            // 如果WebRTC客户端不存在，重新初始化
            if webRTCStreamerClient == nil {
                print("[CameraManager] WebRTC client missing, reinitializing...")
                initializeWebRTC(for: camera)
            }

            // 如果WebRTC未连接，尝试连接
            if !isWebRTCConnected {
                print("[CameraManager] WebRTC not connected, attempting to reconnect...")
                connectWebRTC(to: camera)
            }
        }
    }

    // 更新视频界面可见性
    private func updateVideoInterfaceVisibility() {
        let shouldShow = isConnected && currentCamera != nil && isWebRTCConnected
        print("[CameraManager] Updating shouldShowVideoInterface: \(shouldShow) (isConnected: \(isConnected), hasCamera: \(currentCamera != nil), isWebRTCConnected: \(isWebRTCConnected))")
        shouldShowVideoInterface = shouldShow
    }
    
    func refreshCameraStatus() {
        guard isConnected, let _ = currentCamera else { return }

        // 使用官方API获取摄像机信息
        let infoRequest = GetCameraInfoRequest()

        apiClient.request(infoRequest)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to refresh camera info: \(error)")
                    }
                },
                receiveValue: { [weak self] (response: CameraInfoResponse) in
                    print("Camera info updated: \(response)")
                    // 只在初始连接时获取工作模式，避免频繁请求
                    if self?.workModeLastUpdated == nil ||
                       Date().timeIntervalSince(self?.workModeLastUpdated ?? Date()) > 300 { // 5分钟内不重复获取
                        self?.refreshWorkMode()
                    }
                }
            )
            .store(in: &cancellables)
    }

    // 新增：只在必要时刷新状态（作为WebSocket的备用机制）
    private func refreshCameraStatusIfNeeded() {
        guard isConnected, let _ = currentCamera else { return }

        // 检查WebSocket连接状态
        if !webSocketManager.isConnected {
            print("⚠️ WebSocket disconnected, using HTTP fallback for status update")
            refreshCameraStatus()
        } else {
            print("✅ WebSocket connected, skipping HTTP status update")
        }
    }

    private func refreshWorkMode() {
        print("🔧 Refreshing work mode...")
        let modeRequest = WorkModeRequest(action: .query)

        apiClient.request(modeRequest)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        print("Failed to get work mode: \(error)")
                    } else {
                        self?.workModeLastUpdated = Date()
                    }
                },
                receiveValue: { (response: BasicResponse) in
                    print("Work mode response: \(response)")
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Private Methods

    private func scanForCamerasOnNetwork(completion: @escaping ([CameraDevice]) -> Void) {
        // Simulate camera discovery - in real implementation, this would use mDNS or network scanning
        DispatchQueue.global(qos: .userInitiated).async {
            var discoveredCameras: [CameraDevice] = []

            // Add any previously saved cameras
            discoveredCameras.append(contentsOf: self.loadSavedCameras())

            // Simulate network discovery
            let sampleCamera = CameraDevice(
                name: "ZCam Camera",
                ipAddress: "*************",
                port: 80
            )

            if !discoveredCameras.contains(where: { $0.ipAddress == sampleCamera.ipAddress }) {
                discoveredCameras.append(sampleCamera)
            }

            DispatchQueue.main.async {
                completion(discoveredCameras)
            }
        }
    }
    
    private func setupBindings() {
        // Monitor network status
        networkMonitor.$networkInfo
            .sink { [weak self] networkInfo in
                if networkInfo.status == .notReachable && self?.isConnected == true {
                    self?.handleConnectionFailure(APIError.networkError(NSError(domain: "CameraManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "Network not reachable"])))
                }
            }
            .store(in: &cancellables)
        
        // Monitor WebSocket messages
        webSocketManager.messagePublisher
            .sink { [weak self] message in
                self?.handleWebSocketMessage(message)
            }
            .store(in: &cancellables)
        
        // Monitor WebSocket connection state - 但不影响整体连接状态
        webSocketManager.$connectionState
            .sink { [weak self] state in
                print("[CameraManager] WebSocket state changed: \(state)")
                switch state {
                case .disconnected:
                    print("⚠️ WebSocket disconnected - will attempt reconnection in background")
                    // WebSocket断开不影响整体连接状态，只在后台重连
                case .connected:
                    print("✅ WebSocket reconnected successfully")
                    // WebSocket重连成功，确保WebRTC连接正常
                    if let camera = self?.currentCamera {
                        self?.ensureWebRTCConnection(for: camera)
                    }
                case .failed(let error):
                    print("❌ WebSocket connection failed: \(error.localizedDescription)")
                    // 只有在多次重连失败后才考虑断开整体连接
                case .reconnecting:
                    print("🔄 WebSocket reconnecting...")
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }
    
    private func establishCameraConnection(to camera: CameraDevice) {
        print("🔄 Establishing camera connection to \(camera.ipAddress):\(camera.port)")

        // 首先占用会话
        let sessionRequest = SessionRequest(action: .occupy)
        print("📡 Occupying camera session...")

        apiClient.request(sessionRequest)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] (completion: Subscribers.Completion<APIError>) in
                    if case .failure(let error) = completion {
                        print("❌ Session occupy failed: \(error.localizedDescription)")
                        // 继续尝试获取摄像机信息，即使会话占用失败
                        self?.getCameraInfo(camera: camera)
                    }
                },
                receiveValue: { [weak self] (response: BasicResponse) in
                    if response.code == 0 {
                        print("✅ Camera session occupied successfully")
                    } else {
                        print("⚠️ Session occupy returned code: \(response.code), desc: \(response.desc ?? "Unknown")")
                    }
                    // 继续获取摄像机信息
                    self?.getCameraInfo(camera: camera)
                }
            )
            .store(in: &cancellables)
    }

    private func getCameraInfo(camera: CameraDevice) {
        // 获取摄像机基本信息
        let infoRequest = GetCameraInfoRequest()
        print("📡 Getting camera info...")

        apiClient.request(infoRequest)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] (completion: Subscribers.Completion<APIError>) in
                    if case .failure(let error) = completion {
                        print("❌ Camera info request failed: \(error.localizedDescription)")
                        self?.handleConnectionFailure(error)
                    }
                },
                receiveValue: { [weak self] (response: CameraInfoResponse) in
                    print("✅ Camera info request successful: \(response)")
                    // 更新摄像机信息
                    self?.updateCameraInfo(response)
                    // 建立WebSocket连接
                    self?.connectWebSocket(to: camera)
                }
            )
            .store(in: &cancellables)
    }
    
    private func connectWebSocket(to camera: CameraDevice) {
        print("🔌 Connecting WebSocket to camera...")

        // 根据P2-R1源码分析，WebSocket连接需要：
        // 1. 使用端口81（确认正确）
        // 2. URL末尾需要包含斜杠
        // 3. 需要先检查HTTPS状态来决定使用ws://还是wss://

        // 先检查HTTPS状态，然后建立WebSocket连接
        checkHTTPSStatusAndConnectWebSocket(to: camera)
    }

    private func checkHTTPSStatusAndConnectWebSocket(to camera: CameraDevice) {
        // 根据P2-R1源码，需要先获取https_on状态来决定使用ws://还是wss://
        let httpsRequest = GetParameterRequest(key: "https_on")

        apiClient.request(httpsRequest)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    switch completion {
                    case .failure(let error):
                        print("❌ Failed to get HTTPS status: \(error)")
                        // 如果获取HTTPS状态失败，默认使用ws://
                        self?.establishWebSocketConnection(to: camera, useSSL: false)
                    case .finished:
                        break
                    }
                },
                receiveValue: { [weak self] (response: ParameterResponse) in
                    // 根据https_on的值决定使用ws://还是wss://
                    let useSSL = response.value == "On"
                    print("🔒 HTTPS status: \(response.value), using SSL: \(useSSL)")
                    self?.establishWebSocketConnection(to: camera, useSSL: useSSL)
                }
            )
            .store(in: &cancellables)
    }

    private func establishWebSocketConnection(to camera: CameraDevice, useSSL: Bool) {
        // 基于直接访问P2-R1 Web服务的成功和深入源码分析
        // 浏览器能够成功连接，说明WebSocket服务是正常的
        // 问题可能在于连接方式或头部设置
        let wsProtocol = useSSL ? "wss" : "ws"

        // 基于分析重新排序连接尝试策略：
        // 1. 优先尝试P2-R1源码中明确指定的端口81
        // 2. 然后尝试HTTP端口的WebSocket升级
        // 3. 最后尝试其他可能的端口
        let connectionAttempts = [
            "\(wsProtocol)://\(camera.ipAddress):81/",          // 方式1: 端口81 + 根路径 (P2-R1源码指定)
            "\(wsProtocol)://\(camera.ipAddress):81/ws",        // 方式2: 端口81 + /ws路径
            "\(wsProtocol)://\(camera.ipAddress)/ws",           // 方式3: 端口80 + /ws路径
            "\(wsProtocol)://\(camera.ipAddress):80/ws",        // 方式4: 明确端口80 + /ws路径
            "\(wsProtocol)://\(camera.ipAddress)/websocket",    // 方式5: 端口80 + /websocket路径
            "\(wsProtocol)://\(camera.ipAddress):8080/ws",      // 方式6: 端口8080 + /ws路径
            "\(wsProtocol)://\(camera.ipAddress)"               // 方式7: 无路径（让系统决定）
        ]

        print("🔌 开始WebSocket连接尝试，共\(connectionAttempts.count)种方式")
        // 开始第一次连接尝试
        attemptWebSocketConnection(to: camera, urls: connectionAttempts, currentIndex: 0)
    }

    private func attemptWebSocketConnection(to camera: CameraDevice, urls: [String], currentIndex: Int) {
        guard currentIndex < urls.count else {
            print("❌ All WebSocket connection attempts failed")
            handleConnectionFailure(NSError(domain: "WebSocketError", code: -1, userInfo: [NSLocalizedDescriptionKey: "All WebSocket connection attempts failed"]))
            return
        }

        let wsURL = urls[currentIndex]
        print("🔌 Attempting WebSocket connection (\(currentIndex + 1)/\(urls.count)): \(wsURL)")

        webSocketManager.updateBaseURL(to: wsURL)
        webSocketManager.connect()

        // 存储连接尝试信息以便失败时重试
        currentConnectionAttempt = (camera: camera, urls: urls, currentIndex: currentIndex)

        // Monitor connection state changes
        webSocketManager.$connectionState
            .sink { [weak self] state in
                print("🔌 WebSocket state changed: \(state)")
                switch state {
                case .connected:
                    print("✅ WebSocket connected successfully to: \(wsURL)")
                    self?.currentConnectionAttempt = nil  // 清除重试信息
                    self?.finishConnection()
                case .failed(let error):
                    print("❌ WebSocket connection failed to \(wsURL): \(error.localizedDescription)")
                    self?.handleWebSocketConnectionFailure(error)
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }

    // 存储当前连接尝试信息
    private var currentConnectionAttempt: (camera: CameraDevice, urls: [String], currentIndex: Int)?

    private func handleWebSocketConnectionFailure(_ error: Error) {
        guard let attempt = currentConnectionAttempt else {
            // 如果没有重试信息，使用原来的错误处理
            handleConnectionFailure(error)
            return
        }

        let nextIndex = attempt.currentIndex + 1
        if nextIndex < attempt.urls.count {
            // 尝试下一个URL
            print("🔄 Trying next WebSocket URL...")
            attemptWebSocketConnection(to: attempt.camera, urls: attempt.urls, currentIndex: nextIndex)
        } else {
            // 所有URL都尝试失败
            print("❌ All WebSocket connection attempts exhausted")
            currentConnectionAttempt = nil
            handleConnectionFailure(error)
        }
    }
    
    private func finishConnection() {
        print("🎉 Camera connection established successfully!")

        // 确保在主线程上更新 @Published 属性
        DispatchQueue.main.async {
            print("[CameraManager] Updating isConnected to true on main thread")
            self.isConnected = true
            self.isConnecting = false
            self.connectionError = nil
            print("[CameraManager] isConnected updated: \(self.isConnected)")

            // 更新视频界面可见性
            self.updateVideoInterfaceVisibility()
        }

        // 延迟启动状态更新，优先使用WebSocket消息
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.startStatusUpdates()
        }

        // 给WebSocket一些时间来接收初始状态消息
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            // 如果5秒后仍未通过WebSocket获取到状态，则进行一次HTTP请求作为备用
            if self.batteryLevel == 0 { // 假设电池电量为0表示未获取到状态
                print("⚠️ No status received via WebSocket, performing initial HTTP status check")
                self.refreshCameraStatus()
            } else {
                print("✅ Status already received via WebSocket, skipping initial HTTP check")
            }
        }

        // Initialize WebRTC client but don't auto-connect (让用户选择协议)
        if let camera = currentCamera {
            print("🎥 Initializing WebRTC client (without auto-connect)...")
            Task { @MainActor in
                initializeWebRTC(for: camera)
                print("[CameraManager] WebRTC client initialized: \(webRTCStreamerClient != nil)")
                print("ℹ️ WebRTC client ready but not auto-connecting - user can choose protocol")
            }

            saveLastConnectedCamera(camera)
        }
    }
    
    private func handleConnectionFailure(_ error: Error) {
        print("💥 Connection failed: \(error.localizedDescription)")

        isConnected = false
        isConnecting = false
        connectionError = error

        stopStatusUpdates()
        webSocketManager.disconnect()
    }
    
    private func updateCameraInfo(_ response: CameraInfoResponse) {
        // 更新摄像机基本信息
        print("Camera Model: \(response.model ?? "Unknown")")
        print("Software Version: \(response.sw ?? "Unknown")")
        print("MAC Address: \(response.mac ?? "Unknown")")
        print("IP Address: \(response.eth_ip ?? "Unknown")")
        print("Serial Number: \(response.sn ?? "Unknown")")

        // 标记连接成功 - 确保在主线程上更新 @Published 属性
        DispatchQueue.main.async {
            print("[CameraManager] Updating isConnected to true on main thread (from updateCameraInfo)")
            self.isConnected = true
            self.isConnecting = false
            self.connectionError = nil
            print("[CameraManager] isConnected updated: \(self.isConnected)")
        }

        // 获取更多状态信息
        getCameraSettings()
    }

    private func getCameraSettings() {
        // 获取录制格式
        let formatRequest = GetCameraSettingsRequest(key: "movfmt")

        apiClient.request(formatRequest)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to get recording format: \(error)")
                    }
                },
                receiveValue: { (response: CameraSettingResponse) in
                    if response.code == 0 {
                        print("Recording format: \(response.value ?? "Unknown")")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    private func handleWebSocketMessage(_ message: WebSocketMessage) {
        //print("📨 Received WebSocket message: \(message.type)")

        switch message.type {
        case .statusUpdate:
            if let data = message.data,
               let statusMessage = try? JSONDecoder().decode(StatusUpdateMessage.self, from: data) {
                //print("📊 Status update from WebSocket: battery=\(statusMessage.battery ?? -1), recording=\(statusMessage.recording ?? false)")
                DispatchQueue.main.async {
                    if let battery = statusMessage.battery {
                        self.batteryLevel = battery
                        print("🔋 Battery updated via WebSocket: \(battery)%")
                    }
                    if let recording = statusMessage.recording {
                        if recording != self.isRecording {
                            self.isRecording = recording
                            if recording {
                                self.startRecordingTimer()
                            } else {
                                self.stopRecordingTimer()
                            }
                            print("🎬 Recording status updated via WebSocket: \(recording)")
                        }
                    }
                    if let temperature = statusMessage.temperature {
                        print("🌡️ Temperature updated via WebSocket: \(temperature)°C")
                    }
                    // Handle storage info if needed
                }
            }
        case .recordingStarted:
            print("🔴 Recording started via WebSocket")
            DispatchQueue.main.async {
                self.isRecording = true
                self.startRecordingTimer()
            }
        case .recordingStopped:
            print("⏹️ Recording stopped via WebSocket")
            DispatchQueue.main.async {
                self.isRecording = false
                self.stopRecordingTimer()
            }
        case .configChanged:
            print("⚙️ Configuration changed via WebSocket")
        case .systemTimeChange:
            print("🕐 System time changed via WebSocket")
        case .shutdown:
            print("🔌 Camera shutting down via WebSocket")
            DispatchQueue.main.async {
                self.disconnect()
            }
        default:
            print("📨 Other WebSocket message type: \(message.type)")
        }
    }
    
    private func startStatusUpdates() {
        // 改为更长的间隔，主要依赖WebSocket消息进行状态更新
        // 只作为备用机制，防止WebSocket消息丢失
        statusUpdateTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.refreshCameraStatusIfNeeded()
        }
    }
    
    private func stopStatusUpdates() {
        statusUpdateTimer?.invalidate()
        statusUpdateTimer = nil
    }
    
    private func startRecordingTimer() {
        recordingDuration = 0
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.recordingDuration += 1
        }
    }
    
    private func stopRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTimer = nil
        recordingDuration = 0
    }
    
    // MARK: - Persistence Methods
    
    private func loadSavedCameras() -> [CameraDevice] {
        // Simplified implementation - in real app, this would use CoreData
        let savedCameras = UserDefaults.standard.array(forKey: "SavedCameras") as? [[String: Any]] ?? []

        return savedCameras.compactMap { dict -> CameraDevice? in
            guard let name = dict["name"] as? String,
                  let ipAddress = dict["ipAddress"] as? String,
                  let port = dict["port"] as? Int else {
                return nil
            }

            return CameraDevice(
                name: name,
                ipAddress: ipAddress,
                port: UInt16(port)
            )
        }
    }
    
    private func saveCamerasToStorage(_ cameras: [CameraDevice]) {
        // Simplified implementation using UserDefaults
        let camerasData = cameras.map { camera in
            [
                "name": camera.name,
                "ipAddress": camera.ipAddress,
                "port": camera.port
            ]
        }
        UserDefaults.standard.set(camerasData, forKey: "SavedCameras")
    }

    private func saveCameras(_ cameras: [CameraDevice]) {
        saveCamerasToStorage(cameras)
    }
    
    private func saveLastConnectedCamera(_ camera: CameraDevice) {
        UserDefaults.standard.set(camera.ipAddress, forKey: "last_connected_camera_ip")
    }

    func getLastConnectedCamera() -> CameraDevice? {
        guard let ipAddress = UserDefaults.standard.string(forKey: "last_connected_camera_ip") else {
            return nil
        }

        return availableCameras.first { $0.ipAddress == ipAddress }
    }
}

// MARK: - Camera Status

struct CameraStatus: Codable {
    let isRecording: Bool
    let batteryLevel: Int
    let storageInfo: StorageInfo
    let temperature: Double
    let mode: CameraMode
    let settings: CameraSettings
    let lastActivity: Date
    
    enum CameraMode: String, Codable, CaseIterable {
        case photo = "photo"
        case video = "video"
        case timelapse = "timelapse"
        case burst = "burst"
        
        var displayName: String {
            switch self {
            case .photo: return "Photo"
            case .video: return "Video"
            case .timelapse: return "Timelapse"
            case .burst: return "Burst"
            }
        }
    }
}

// MARK: - Camera Settings

struct CameraSettings: Codable {
    let resolution: VideoResolution
    let frameRate: Int
    let quality: String
    let autoFocus: Bool
    let whiteBalance: WhiteBalanceMode
    let exposureMode: ExposureMode
    let iso: Int?
    let shutterSpeed: Double?

    enum WhiteBalanceMode: String, Codable, CaseIterable {
        case auto = "auto"
        case daylight = "daylight"
        case cloudy = "cloudy"
        case tungsten = "tungsten"
        case fluorescent = "fluorescent"
        case manual = "manual"
    }

    enum ExposureMode: String, Codable, CaseIterable {
        case auto = "auto"
        case manual = "manual"
        case aperturePriority = "aperture_priority"
        case shutterPriority = "shutter_priority"
    }
}

// MARK: - Storage Info

struct StorageInfo: Codable {
    let totalSpace: Int64
    let usedSpace: Int64
    let availableSpace: Int64
    let recordingTimeRemaining: TimeInterval

    var usagePercentage: Double {
        guard totalSpace > 0 else { return 0 }
        return Double(usedSpace) / Double(totalSpace)
    }
    
    var formattedTotalSpace: String {
        ByteCountFormatter.string(fromByteCount: totalSpace, countStyle: .file)
    }
    
    var formattedUsedSpace: String {
        ByteCountFormatter.string(fromByteCount: usedSpace, countStyle: .file)
    }
    
    var formattedAvailableSpace: String {
        ByteCountFormatter.string(fromByteCount: availableSpace, countStyle: .file)
    }
}



// MARK: - Gallery Manager

class GalleryManager: ObservableObject {
    static let shared = GalleryManager()
    
    @Published var files: [CameraFile] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private let apiClient = APIClient.shared
    private let cameraManager = CameraManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {}
    
    func loadFiles() {
        guard cameraManager.currentCamera != nil else { return }
        
        isLoading = true
        error = nil
        
        let request = GetFileListRequest()
        
        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] (response: DCIMResponse) in
                    // 处理DCIM目录响应
                    if let files = response.files {
                        // 将文件名转换为CameraFile对象
                        let cameraFiles = files.compactMap { filename -> CameraFile? in
                            return CameraFile(
                                id: UUID(),
                                name: filename,
                                type: filename.hasSuffix(".MOV") ? .video : .photo,
                                size: 0, // 需要单独获取
                                createdAt: Date(),
                                path: "/DCIM/\(filename)"
                            )
                        }
                        self?.files = cameraFiles
                    } else if let folders = response.folders {
                        // 如果返回的是文件夹列表，选择第一个文件夹继续获取
                        if let firstFolder = folders.first, let strongSelf = self {
                            let folderRequest = GetFileListRequest(folder: firstFolder)
                            strongSelf.apiClient.request(folderRequest)
                                .receive(on: DispatchQueue.main)
                                .sink(
                                    receiveCompletion: { _ in },
                                    receiveValue: { [weak self] (folderResponse: DCIMResponse) in
                                        if let files = folderResponse.files {
                                            let cameraFiles = files.compactMap { filename -> CameraFile? in
                                                return CameraFile(
                                                    id: UUID(),
                                                    name: filename,
                                                    type: filename.hasSuffix(".MOV") ? .video : .photo,
                                                    size: 0,
                                                    createdAt: Date(),
                                                    path: "/DCIM/\(firstFolder)/\(filename)"
                                                )
                                            }
                                            self?.files = cameraFiles
                                        }
                                    }
                                )
                                .store(in: &strongSelf.cancellables)
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func refreshFiles() {
        loadFiles()
    }
    
    func filteredFiles(for filter: FileFilter) -> [CameraFile] {
        switch filter {
        case .all:
            return files
        case .photos:
            return files.filter { $0.type == .photo }
        case .videos:
            return files.filter { $0.type == .video }
        case .recent:
            return Array(files.sorted { $0.createdAt > $1.createdAt }.prefix(20))
        }
    }
}

enum FileFilter: String, CaseIterable {
    case all = "all"
    case photos = "photos"
    case videos = "videos"
    case recent = "recent"
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .photos: return "Photos"
        case .videos: return "Videos"
        case .recent: return "Recent"
        }
    }
}

