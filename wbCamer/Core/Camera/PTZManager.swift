//
//  PTZManager.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Combine

// MARK: - PTZ Manager

@MainActor
class PTZManager: ObservableObject {
    static let shared = PTZManager()
    
    @Published var isControlling = false
    @Published var currentPTZAction: PTZControlRequest.PTZAction?
    @Published var currentZoomAction: ZoomControlRequest.ZoomAction?
    
    private var apiClient: APIClient?
    private var cancellables = Set<AnyCancellable>()
    private var ptzTimer: Timer?
    private var zoomTimer: Timer?

    // 延迟优化：专用高优先级网络队列
    private let ptzQueue = DispatchQueue(
        label: "ptz.network.queue",
        qos: .userInteractive,
        attributes: .concurrent
    )

    // 延迟优化：命令去重
    private var lastPTZCommand: (pan: Float, tilt: Float, timestamp: Date)?
    private let commandThreshold: TimeInterval = 0.05  // 50ms去重阈值
    
    private init() {}
    
    // MARK: - Configuration
    
    func configure(with apiClient: APIClient) {
        self.apiClient = apiClient
    }
    
    // MARK: - PTZ Control

    // 单方向PTZ控制 (directionMove)
    func executePTZAction(_ action: PTZControlRequest.PTZAction, fspeed: Float = 0.5) {
        guard apiClient != nil else {
            print("[PTZManager] ❌ API client not configured")
            return
        }

        currentPTZAction = action

        // 停止之前的PTZ定时器
        ptzTimer?.invalidate()

        if action == .stop {
            print("[PTZManager] 🛑 PTZ Stop")
            sendPTZStopCommand()
            currentPTZAction = nil
            return
        }

        print("[PTZManager] 🎮 PTZ Action: \(action), fspeed: \(fspeed)")

        // 立即发送命令
        sendPTZDirectionCommand(action, fspeed: fspeed)
    }

    // 同时控制pan和tilt (ptMove) - 这是主要的摇杆控制方法
    func executePTZMove(panSpeed: Float, tiltSpeed: Float) {
        guard apiClient != nil else {
            print("[PTZManager] ❌ API client not configured")
            return
        }

        // 停止之前的PTZ定时器
        ptzTimer?.invalidate()

        // 如果速度都为0，发送停止命令
        if abs(panSpeed) < 0.01 && abs(tiltSpeed) < 0.01 {
            print("[PTZManager] 🛑 PTZ Stop (pan: \(panSpeed), tilt: \(tiltSpeed))")
            sendPTZStopCommand()
            currentPTZAction = nil
            lastPTZCommand = nil  // 清除上次命令记录
            return
        }

        // 延迟优化：命令去重，避免发送重复命令
        let now = Date()
        if let lastCommand = lastPTZCommand,
           abs(lastCommand.pan - panSpeed) < 0.01,
           abs(lastCommand.tilt - tiltSpeed) < 0.01,
           now.timeIntervalSince(lastCommand.timestamp) < commandThreshold {
            return  // 跳过重复命令
        }

        lastPTZCommand = (panSpeed, tiltSpeed, now)
        print("[PTZManager] 🎮 PTZ Move: pan=\(panSpeed), tilt=\(tiltSpeed)")

        // 发送PTZ移动命令
        sendPTZMoveCommand(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
    }

    private func sendPTZDirectionCommand(_ action: PTZControlRequest.PTZAction, fspeed: Float) {
        guard let apiClient = apiClient else { return }

        let request = PTZControlRequest(action: action, fspeed: fspeed)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("[PTZManager] ❌ PTZ direction command failed: \(error)")
                    }
                },
                receiveValue: { response in
                    print("[PTZManager] ✅ PTZ direction command sent: \(action), fspeed: \(fspeed)")
                }
            )
            .store(in: &cancellables)
    }

    private func sendPTZMoveCommand(panSpeed: Float, tiltSpeed: Float) {
        guard let apiClient = apiClient else { return }

        let request = PTZControlRequest(panSpeed: panSpeed, tiltSpeed: tiltSpeed)

        // 延迟监控：开始测量PTZ命令延迟
        let measurementId = LatencyMonitor.shared.startPTZLatencyMeasurement()

        // 延迟优化：使用专用高优先级队列发送PTZ命令
        // 修复Sendable警告：在主线程上发送请求，避免跨线程捕获问题
        let publisher = apiClient.request(request)
            .receive(on: DispatchQueue.main)

        publisher.sink(
            receiveCompletion: { completion in
                // 延迟监控：结束测量
                LatencyMonitor.shared.endPTZLatencyMeasurement(measurementId)

                if case .failure(let error) = completion {
                    print("[PTZManager] ❌ PTZ move command failed: \(error)")
                }
            },
            receiveValue: { response in
                // 延迟监控：结束测量
                LatencyMonitor.shared.endPTZLatencyMeasurement(measurementId)
                print("[PTZManager] ✅ PTZ move command sent: pan=\(panSpeed), tilt=\(tiltSpeed)")
            }
        )
        .store(in: &cancellables)
    }

    private func sendPTZStopCommand() {
        guard let apiClient = apiClient else { return }

        let request = PTZControlRequest(stopAll: false)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("[PTZManager] ❌ PTZ stop command failed: \(error)")
                    }
                },
                receiveValue: { response in
                    print("[PTZManager] ✅ PTZ stop command sent")
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Zoom Control

    func executeZoomAction(_ action: ZoomControlRequest.ZoomAction, fspeed: Float = 0.5) {
        guard apiClient != nil else {
            print("[PTZManager] ❌ API client not configured")
            return
        }

        currentZoomAction = action

        // 停止之前的变焦定时器
        zoomTimer?.invalidate()

        // 检查是否是停止操作
        if case .stop = action {
            print("[PTZManager] 🛑 Zoom Stop")
            sendZoomCommand(action, fspeed: fspeed)
            currentZoomAction = nil
            return
        }

        print("[PTZManager] 🔍 Zoom Action: \(action), fspeed: \(fspeed)")

        // 立即发送命令
        sendZoomCommand(action, fspeed: fspeed)
    }

    private func sendZoomCommand(_ action: ZoomControlRequest.ZoomAction, fspeed: Float = 0.5) {
        guard let apiClient = apiClient else { return }

        let request = ZoomControlRequest(action: action, fspeed: fspeed)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("[PTZManager] ❌ Zoom command failed: \(error)")
                    }
                },
                receiveValue: { response in
                    print("[PTZManager] ✅ Zoom command sent: \(action), fspeed: \(fspeed)")
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Focus Control
    
    func executeFocusAction(_ action: FocusControlRequest.FocusAction) {
        guard let apiClient = apiClient else {
            print("[PTZManager] ❌ API client not configured")
            return
        }
        
        print("[PTZManager] 🎯 Focus Action: \(action)")
        
        let request = FocusControlRequest(action: action)
        
        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("[PTZManager] ❌ Focus command failed: \(error)")
                    }
                },
                receiveValue: { response in
                    print("[PTZManager] ✅ Focus command sent: \(action)")
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Cleanup
    
    func stopAllActions() {
        print("[PTZManager] 🛑 Stopping all PTZ actions")
        
        ptzTimer?.invalidate()
        zoomTimer?.invalidate()
        
        if currentPTZAction != nil {
            sendPTZStopCommand()
            currentPTZAction = nil
        }

        if currentZoomAction != nil {
            sendZoomCommand(.stop, fspeed: 0.5)
            currentZoomAction = nil
        }
        
        isControlling = false
    }
    
    deinit {
        ptzTimer?.invalidate()
        zoomTimer?.invalidate()
        cancellables.removeAll()
    }
}

// MARK: - PTZ Control Extensions

extension PTZManager {

    // 便捷方法：移动到指定方向 (单方向控制)
    func moveToDirection(_ direction: JoystickDirection, fspeed: Float = 0.5) {
        executePTZAction(direction.ptzAction, fspeed: fspeed)
    }

    // 便捷方法：停止移动
    func stopMovement() {
        executePTZAction(PTZControlRequest.PTZAction.stop)
    }

    // 便捷方法：摇杆控制 (同时控制pan和tilt)
    func moveWithJoystick(panSpeed: Float, tiltSpeed: Float) {
        executePTZMove(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
    }

    // 便捷方法：变焦放大
    func zoomIn(fspeed: Float = 0.5) {
        executeZoomAction(ZoomControlRequest.ZoomAction.zoomIn, fspeed: fspeed)
    }

    // 便捷方法：变焦缩小
    func zoomOut(fspeed: Float = 0.5) {
        executeZoomAction(ZoomControlRequest.ZoomAction.zoomOut, fspeed: fspeed)
    }

    // 便捷方法：停止变焦
    func stopZoom() {
        executeZoomAction(ZoomControlRequest.ZoomAction.stop)
    }

    // 便捷方法：自动对焦
    func autoFocus() {
        executeFocusAction(FocusControlRequest.FocusAction.autoFocus)
    }
}
