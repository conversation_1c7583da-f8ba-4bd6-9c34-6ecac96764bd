//
//  DeviceTypeDetector.swift
//  wbCamer
//
//  Created by AI Assistant on 2025/6/30.
//

import UIKit
import SwiftUI

// MARK: - Device Type Detection

enum DeviceType {
    case iPhone
    case iPad
    
    var isPhone: Bool {
        return self == .iPhone
    }
    
    var isTablet: Bool {
        return self == .iPad
    }
}

class DeviceTypeDetector: ObservableObject {
    static let shared = DeviceTypeDetector()
    
    @Published var currentDeviceType: DeviceType
    
    private init() {
        self.currentDeviceType = DeviceTypeDetector.detectDeviceType()
    }
    
    static func detectDeviceType() -> DeviceType {
        switch UIDevice.current.userInterfaceIdiom {
        case .phone:
            return .iPhone
        case .pad:
            return .iPad
        default:
            // 默认为iPhone，包括其他未知设备类型
            return .iPhone
        }
    }
    
    /// 获取当前设备类型
    var deviceType: DeviceType {
        return currentDeviceType
    }
    
    /// 是否为iPhone
    var isPhone: Bool {
        return currentDeviceType.isPhone
    }
    
    /// 是否为iPad
    var isTablet: Bool {
        return currentDeviceType.isTablet
    }
    
    /// 获取适合当前设备的摇杆大小 (已扩大30%)
    var joystickSize: CGFloat {
        switch currentDeviceType {
        case .iPhone:
            return 208  // iPhone: 160 * 1.3 = 208
        case .iPad:
            return 260  // iPad: 200 * 1.3 = 260
        }
    }
    
    /// 获取适合当前设备的控制面板高度
    var controlPanelHeight: CGFloat {
        switch currentDeviceType {
        case .iPhone:
            return 120  // iPhone使用较小的控制面板
        case .iPad:
            return 150  // iPad使用较大的控制面板
        }
    }
    
    /// 获取适合当前设备的边距
    var defaultPadding: CGFloat {
        switch currentDeviceType {
        case .iPhone:
            return 16
        case .iPad:
            return 24
        }
    }
    
    /// 获取适合当前设备的摇杆边距
    var joystickPadding: CGFloat {
        switch currentDeviceType {
        case .iPhone:
            return 20
        case .iPad:
            return 30
        }
    }
}

// MARK: - SwiftUI Environment Key

struct DeviceTypeKey: EnvironmentKey {
    static let defaultValue: DeviceType = DeviceTypeDetector.detectDeviceType()
}

extension EnvironmentValues {
    var deviceType: DeviceType {
        get { self[DeviceTypeKey.self] }
        set { self[DeviceTypeKey.self] = newValue }
    }
}

// MARK: - SwiftUI View Extensions

extension View {
    /// 根据设备类型应用不同的修饰符
    func deviceSpecific<iPhone: View, iPad: View>(
        iPhone: () -> iPhone,
        iPad: () -> iPad
    ) -> some View {
        Group {
            if DeviceTypeDetector.shared.isPhone {
                iPhone()
            } else {
                iPad()
            }
        }
    }
    
    /// 根据设备类型设置不同的padding
    func devicePadding() -> some View {
        self.padding(DeviceTypeDetector.shared.defaultPadding)
    }
    
    /// 强制横屏显示
    func forceLandscape() -> some View {
        self.onAppear {
            AppDelegate.orientationLock = .landscape
            UIDevice.current.setValue(UIInterfaceOrientation.landscapeLeft.rawValue, forKey: "orientation")
        }
        .onDisappear {
            AppDelegate.orientationLock = .all
        }
    }
}

// MARK: - App Delegate Extension for Orientation Control

class AppDelegate: NSObject, UIApplicationDelegate {
    static var orientationLock = UIInterfaceOrientationMask.all
    
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return AppDelegate.orientationLock
    }
}
